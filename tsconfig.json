{"compilerOptions": {"target": "es6", "lib": ["es6", "dom"], "strict": false, "noImplicitAny": false, "strictNullChecks": false, "strictFunctionTypes": false, "strictBindCallApply": false, "strictPropertyInitialization": false, "noImplicitThis": false, "alwaysStrict": false, "noUnusedLocals": false, "noUnusedParameters": false, "noImplicitReturns": false, "noFallthroughCasesInSwitch": false, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": false, "noPropertyAccessFromIndexSignature": false, "noUncheckedIndexedAccess": false, "typeRoots": ["./node_modules/@types", "./node_modules/@figma"]}, "include": ["src/**/*.ts", "src/**/*.tsx"], "exclude": ["node_modules"]}