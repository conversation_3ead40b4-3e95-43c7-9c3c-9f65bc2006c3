<div class="fpw-card-overlay">
  <div class="fpw-card">
    <main class='section fpw-main-section'>
      <nav class='fpw-navigation'>
        <ul class="fpw-flex fpw-navigation-list">
          <li class="fpw-font-base fpw-navigation-item active-tag" data-tab-index="0">API
            Key</li>
          <li class="fpw-font-base fpw-navigation-item" data-tab-index="1">
            Guidelines</li>
          <li class="fpw-font-base fpw-navigation-item" data-tab-index="2">
            Select Layer</li>
          <li class="fpw-font-base fpw-navigation-item" data-tab-index="3">
            Generate</li>
        </ul>
      </nav>
      <div class='fpw-content'>
        <div class="fpw-apikey fpw-item-active">
          <div class="fpw-apikey-wrapper">
            <h2 class="fpw-font-base fpw-content-title">Figma API Key</h2>

            <form class="fpw-flex fpw-apikey-form">
              <!-- figd_T5hbshP-ygo-QfMhlNLOwNMPmfsFeMeotJll4Nef -->
              <input type="text" id="fpw-figma-api-key" class="fpw-font-base fpw-apikey-pest-field "
                placeholder="Paste your API key" />
            </form>

            <p class="fpw-font-base fpw-generate-notification">
              You can generate your API key from your <a href="#">Figma</a> account
              settings.
            </p>
            <p class="fpw-font-base fpw-generate-notification">
              👉🏻 Make sure it's active and has access to the file you're working on

            </p>

            <p class="fpw-font-base fpw-error-notification">Oops! That API key doesn’t seem to be
              working. Double-check the key and try again — or generate a new one.</p>
          </div>
        </div>

        <div class="fpw-guidelines fpw-item-inactive">
          <div class="fpw-guidelines-wrapper">
            <h2 class="fpw-font-base fpw-content-title">Plugin Usage Guidelines</h2>
            <p class="fpw-font-base fpw-guidelines-description">Follow these essential steps to ensure
              everything works smoothly:</p>
            <ol class="fpw-guidelines-list">
              <li class="fpw-font-base fpw-guidelines-list-item">Use a valid and active Figma API key</li>
              <li class="fpw-font-base fpw-guidelines-list-item">Confirm you have edit access to the file
              </li>
              <li class="fpw-font-base fpw-guidelines-list-item">Use “Auto Layout” wherever possible to
                avoid design breakage.</li>
              <li class="fpw-font-base fpw-guidelines-list-item">Use “Containers” if column layout breaks
                (optional but helpful).</li>
              <li class="fpw-font-base fpw-guidelines-list-item">All Sections & Inner Sections must
                contain <span class="fpw-bold">“Columns”.</span> </li>
              <li class="fpw-font-base fpw-guidelines-list-item">Frames or layers must be named using our
                supported Elementor widget names:</li>
            </ol>

            <ul class="fpw-flex fpw-guideline-layer-name-list ">
              <li class="fpw-font-base fpw-guideline-layer-name-item">Container</li>
              <li class="fpw-font-base fpw-guideline-layer-name-item">Button</li>
              <li class="fpw-font-base fpw-guideline-layer-name-item">Heading</li>
              <li class="fpw-font-base fpw-guideline-layer-name-item">Text Editor</li>
              <li class="fpw-font-base fpw-guideline-layer-name-item">Text Editor</li>
              <li class="fpw-font-base fpw-guideline-layer-name-item">Creative Button</li>
              <li class="fpw-font-base fpw-guideline-layer-name-item">Dual Color Heading</li>
              <li class="fpw-font-base fpw-guideline-layer-name-item">Advanced Menu</li>
              <li class="fpw-font-base fpw-guideline-layer-name-item">Call to Action</li>
              <li class="fpw-font-base fpw-guideline-layer-name-item">Info Box</li>
              <li class="fpw-font-base fpw-guideline-layer-name-item">Post Carousel</li>
              <li class="fpw-font-base fpw-guideline-layer-name-item">Testimonial</li>
              <li class="fpw-font-base fpw-guideline-layer-name-item">Feature List</li>
              <li class="fpw-font-base fpw-guideline-layer-name-item">Counter</li>
              <li class="fpw-font-base fpw-guideline-layer-name-item">Logo Carousel</li>
              <li class="fpw-font-base fpw-guideline-layer-name-item">Logo Carousel</li>
              <li class="fpw-font-base fpw-guideline-layer-name-item">Woo Product List</li>


            </ul>
          </div>
        </div>

        <div class="fpw-select-layer fpw-item-inactive">
          <div class="fpw-select-layer-wrapper">

            <div class="fpw-generate-load-wrapper fpw-item-inactive">
              <div class="fpw-generate-load fpw-lowding-box">
                <svg width="60" height="60" viewBox="0 0 50 50">
                  <line x1="25" y1="10" x2="25" y2="15" stroke="#BFBFBF" stroke-width="3" stroke-linecap="round"
                    transform="rotate(0 25 25)">
                    <animate attributeName="opacity" values="0.2;1;0.2" dur="1.2s" begin="0s"
                      repeatCount="indefinite" />
                  </line>
                  <line x1="25" y1="10" x2="25" y2="15" stroke="#BFBFBF" stroke-width="3" stroke-linecap="round"
                    transform="rotate(45 25 25)">
                    <animate attributeName="opacity" values="0.2;1;0.2" dur="1.2s" begin="0.15s"
                      repeatCount="indefinite" />
                  </line>
                  <line x1="25" y1="10" x2="25" y2="15" stroke="#BFBFBF" stroke-width="3" stroke-linecap="round"
                    transform="rotate(90 25 25)">
                    <animate attributeName="opacity" values="0.2;1;0.2" dur="1.2s" begin="0.3s"
                      repeatCount="indefinite" />
                  </line>
                  <line x1="25" y1="10" x2="25" y2="15" stroke="#BFBFBF" stroke-width="3" stroke-linecap="round"
                    transform="rotate(135 25 25)">
                    <animate attributeName="opacity" values="0.2;1;0.2" dur="1.2s" begin="0.45s"
                      repeatCount="indefinite" />
                  </line>
                  <line x1="25" y1="10" x2="25" y2="15" stroke="#BFBFBF" stroke-width="3" stroke-linecap="round"
                    transform="rotate(180 25 25)">
                    <animate attributeName="opacity" values="0.2;1;0.2" dur="1.2s" begin="0.6s"
                      repeatCount="indefinite" />
                  </line>
                  <line x1="25" y1="10" x2="25" y2="15" stroke="#BFBFBF" stroke-width="3" stroke-linecap="round"
                    transform="rotate(225 25 25)">
                    <animate attributeName="opacity" values="0.2;1;0.2" dur="1.2s" begin="0.75s"
                      repeatCount="indefinite" />
                  </line>
                  <line x1="25" y1="10" x2="25" y2="15" stroke="#BFBFBF" stroke-width="3" stroke-linecap="round"
                    transform="rotate(270 25 25)">
                    <animate attributeName="opacity" values="0.2;1;0.2" dur="1.2s" begin="0.9s"
                      repeatCount="indefinite" />
                  </line>
                  <line x1="25" y1="10" x2="25" y2="15" stroke="#BFBFBF" stroke-width="3" stroke-linecap="round"
                    transform="rotate(315 25 25)">
                    <animate attributeName="opacity" values="0.2;1;0.2" dur="1.2s" begin="1.05s"
                      repeatCount="indefinite" />
                  </line>
                </svg>
              </div>
            </div>

            <div class="fpw-text-wrapper ">
              <div class="fpw-before-generate-header">
                <h2 class="fpw-font-base fpw-content-title">Select a Layer to Begin</h2>
                <p class="fpw-font-base fpw-guidelines-description" style="padding: 0 20px;">Select a
                  frame from your Figma file to use with the plugin. Only layers with proper widget
                  names will be processed.</p>
              </div>
              <div class="fpw-after-generate-header">
                <p class="fpw-font-base fpw-guidelines-description">You’ve successfully selected a
                  layer to work with</p>
              </div>
            </div>

            <div class='fpw-fig-input-wrapper'>

              <form>
                <input type="file" id="figFileInput" accept=".fig" class="fpw-fig-input" hidden />
                <label for="figFileInput" class="fpw-font-base fpw-fig-input-custom-field">
                  Select a Layer
                </label>
                <div class='fpw-fig-output-custom-field fpw-item-inactive'>
                  <img src="" alt="" />
                </div>
              </form>
            </div>
          </div>
        </div>

        <div class="fpw-generate fpw-item-inactive">
          <div class="fpw-generate-wrapper">
            <div class="fpw-text-wrapper">
              <h2 class="fpw-font-base fpw-content-title"></h2>
              <p class="fpw-font-base fpw-guidelines-description">Sit back, this can take a couple
                minutes</p>
            </div>
            <!-- generate loader  -->
            <div class="fpw-generate-load-wrapper">
              <div class="fpw-generate-load">
                <svg width="60" height="60" viewBox="0 0 50 50">
                  <line x1="25" y1="10" x2="25" y2="15" stroke="#BFBFBF" stroke-width="3" stroke-linecap="round"
                    transform="rotate(0 25 25)">
                    <animate attributeName="opacity" values="0.2;1;0.2" dur="1.2s" begin="0s"
                      repeatCount="indefinite" />
                  </line>
                  <line x1="25" y1="10" x2="25" y2="15" stroke="#BFBFBF" stroke-width="3" stroke-linecap="round"
                    transform="rotate(45 25 25)">
                    <animate attributeName="opacity" values="0.2;1;0.2" dur="1.2s" begin="0.15s"
                      repeatCount="indefinite" />
                  </line>
                  <line x1="25" y1="10" x2="25" y2="15" stroke="#BFBFBF" stroke-width="3" stroke-linecap="round"
                    transform="rotate(90 25 25)">
                    <animate attributeName="opacity" values="0.2;1;0.2" dur="1.2s" begin="0.3s"
                      repeatCount="indefinite" />
                  </line>
                  <line x1="25" y1="10" x2="25" y2="15" stroke="#BFBFBF" stroke-width="3" stroke-linecap="round"
                    transform="rotate(135 25 25)">
                    <animate attributeName="opacity" values="0.2;1;0.2" dur="1.2s" begin="0.45s"
                      repeatCount="indefinite" />
                  </line>
                  <line x1="25" y1="10" x2="25" y2="15" stroke="#BFBFBF" stroke-width="3" stroke-linecap="round"
                    transform="rotate(180 25 25)">
                    <animate attributeName="opacity" values="0.2;1;0.2" dur="1.2s" begin="0.6s"
                      repeatCount="indefinite" />
                  </line>
                  <line x1="25" y1="10" x2="25" y2="15" stroke="#BFBFBF" stroke-width="3" stroke-linecap="round"
                    transform="rotate(225 25 25)">
                    <animate attributeName="opacity" values="0.2;1;0.2" dur="1.2s" begin="0.75s"
                      repeatCount="indefinite" />
                  </line>
                  <line x1="25" y1="10" x2="25" y2="15" stroke="#BFBFBF" stroke-width="3" stroke-linecap="round"
                    transform="rotate(270 25 25)">
                    <animate attributeName="opacity" values="0.2;1;0.2" dur="1.2s" begin="0.9s"
                      repeatCount="indefinite" />
                  </line>
                  <line x1="25" y1="10" x2="25" y2="15" stroke="#BFBFBF" stroke-width="3" stroke-linecap="round"
                    transform="rotate(315 25 25)">
                    <animate attributeName="opacity" values="0.2;1;0.2" dur="1.2s" begin="1.05s"
                      repeatCount="indefinite" />
                  </line>
                </svg>
              </div>
            </div>
          </div>

        </div>

        <div class="fpw-complete-concert fpw-item-inactive">

          <div class="fpw-success-wrapper">
            <div class="fpw-text-wrapper">
              <div class="fpw-flex fpw-gap-3  fpw-justify-content-center fpw-align-items-center"
                style="margin-bottom: 8px;">
                <span class="fpw-img-wrapper line-height-0">
                  <svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <g clip-path="url(#clip0_10_280)">
                      <rect width="14" height="14" rx="7" fill="#383838" />
                      <mask id="mask0_10_280" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="0" y="0"
                        width="14" height="14">
                        <path d="M14 0H0V14H14V0Z" fill="white" />
                      </mask>
                      <g mask="url(#mask0_10_280)">
                        <path
                          d="M7 0C3.14005 0 0 3.14005 0 7C0 10.86 3.14005 14 7 14C10.86 14 14 10.86 14 7C14 3.14005 10.86 0 7 0Z"
                          fill="white" fill-opacity="0.06" />
                        <path
                          d="M10.5479 5.51642L6.75621 9.30802C6.64246 9.42177 6.49313 9.47902 6.34381 9.47902C6.19449 9.47902 6.04517 9.42177 5.93141 9.30802L4.03561 7.41222C3.80746 7.18418 3.80746 6.81546 4.03561 6.58742C4.26366 6.35927 4.63226 6.35927 4.86041 6.58742L6.34381 8.07082L9.72311 4.69162C9.95116 4.46347 10.3198 4.46347 10.5479 4.69162C10.776 4.91967 10.776 5.28827 10.5479 5.51642Z"
                          fill="white" />
                      </g>
                    </g>
                    <defs>
                      <clipPath id="clip0_10_280">
                        <rect width="14" height="14" rx="7" fill="white" />
                      </clipPath>
                    </defs>
                  </svg>


                </span>
                <h2 class="fpw-font-base fpw-content-title fpw-margin-bottom-0">Success! You’re All Set
                </h2>
              </div>
              <p class="fpw-font-base fpw-guidelines-description">
                Your layer has been processed successfully.
              </p>
            </div>

            <div class="fpw-generate-load-wrapper">
              <code id="code-block" class="fpw-font-base fpw-generate-load-code">
                              &lt;div&gt;This is a div tag&lt;/div&gt;
                          </code>
            </div>

            <button id="fwp-json-copy" class="fpw-flex fpw-font-base fpw-code-copy-button">
              <span class="line-height-0"><svg width="16" height="16" viewBox="0 0 16 16" fill="none"
                  xmlns="http://www.w3.org/2000/svg">
                  <g clip-path="url(#clip0_76_818)">
                    <path
                      d="M4.66663 6.44466C4.66663 5.97311 4.85395 5.52087 5.18739 5.18743C5.52083 4.85399 5.97307 4.66666 6.44463 4.66666H12.222C12.4554 4.66666 12.6867 4.71265 12.9024 4.80201C13.1181 4.89136 13.3141 5.02233 13.4792 5.18743C13.6443 5.35253 13.7753 5.54854 13.8646 5.76425C13.954 5.97997 14 6.21117 14 6.44466V12.222C14 12.4555 13.954 12.6867 13.8646 12.9024C13.7753 13.1181 13.6443 13.3141 13.4792 13.4792C13.3141 13.6443 13.1181 13.7753 12.9024 13.8647C12.6867 13.954 12.4554 14 12.222 14H6.44463C6.21114 14 5.97993 13.954 5.76421 13.8647C5.5485 13.7753 5.35249 13.6443 5.18739 13.4792C5.02229 13.3141 4.89132 13.1181 4.80197 12.9024C4.71262 12.6867 4.66663 12.4555 4.66663 12.222V6.44466Z"
                      stroke="white" stroke-linecap="round" stroke-linejoin="round" />
                    <path
                      d="M2.67467 11.158C2.47023 11.0415 2.30018 10.873 2.18172 10.6697C2.06325 10.4663 2.00057 10.2353 2 10V3.33333C2 2.6 2.6 2 3.33333 2H10C10.5 2 10.772 2.25667 11 2.66667"
                      stroke="white" stroke-linecap="round" stroke-linejoin="round" />
                  </g>
                  <defs>
                    <clipPath id="clip0_76_818">
                      <rect width="16" height="16" fill="white" />
                    </clipPath>
                  </defs>
                </svg>
              </span>
              <span>Copy Code</span>
            </button>

            <div class="fpw-flex fpw-gap-3">
              <button id="fwp-json-download" class="fpw-flex fpw-font-base fpw-download-button">
                <span class="line-height-0"><svg width="16" height="16" viewBox="0 0 16 16" fill="none"
                    xmlns="http://www.w3.org/2000/svg">
                    <g clip-path="url(#clip0_76_824)">
                      <path
                        d="M2.66663 11.3333V12.6667C2.66663 13.0203 2.8071 13.3594 3.05715 13.6095C3.3072 13.8595 3.64634 14 3.99996 14H12C12.3536 14 12.6927 13.8595 12.9428 13.6095C13.1928 13.3594 13.3333 13.0203 13.3333 12.6667V11.3333"
                        stroke="#F1F1F1" stroke-linecap="round" stroke-linejoin="round" />
                      <path d="M4.66663 7.33333L7.99996 10.6667L11.3333 7.33333" stroke="#F1F1F1" stroke-linecap="round"
                        stroke-linejoin="round" />
                      <path d="M8 2.66666V10.6667" stroke="#F1F1F1" stroke-linecap="round" stroke-linejoin="round" />
                    </g>
                    <defs>
                      <clipPath id="clip0_76_824">
                        <rect width="16" height="16" fill="white" />
                      </clipPath>
                    </defs>
                  </svg>
                </span>
                <span>Download</span>
              </button>

              <button class="fpw-flex fpw-font-base fpw-generate-another-file fpw-download-button">
                <span>Generate Another File</span>
                <span class="line-height-0">
                  <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path
                      d="M12.6667 8.00001C12.6667 10.5773 10.5773 12.6667 8.00001 12.6667C5.42268 12.6667 3.33334 10.5773 3.33334 8.00001C3.33334 5.42268 5.42268 3.33334 8.00001 3.33334"
                      stroke="white" stroke-linecap="round" stroke-linejoin="round" />
                    <path d="M8 1.33334V3.33334" stroke="white" stroke-linecap="round" stroke-linejoin="round" />
                    <path d="M12.6667 3.33334H10.6667" stroke="white" stroke-linecap="round" stroke-linejoin="round" />
                  </svg>
                </span>
              </button>
            </div>
            <p class="fpw-font-base  fpw-contract-support-team">Got stuck? Please reach out to our <a href="#"> Support
                team</a></p>

          </div>

        </div>


      </div>

      <div class="fpw-continue-button-wrapper">
        <form class="fpw-guidelines-support-btn fpw-item-inactive">
          <label htmlFor="ISupport" class="fpw-font-base fpw-guidelines-support">
            <input type="checkbox" id="ISupport" />
            <label htmlFor="ISupport" class="support-checkbox"></label>
            I have done them properly
          </label>
        </form>
        <div class="fpw-main-button-wrapper">
          <button class="fpw-flex fpw-font-base fpw-continue-button fpw-button-disabled">
            <span>Save and Continue</span>
            <span class="line-height-0">
              <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                <g clip-path="url(#clip0_76_521)">
                  <path d="M5.8401 3.68002L10.1601 8.00002L5.8401 12.32" stroke="white" stroke-width="1.08"
                    stroke-linecap="round" stroke-linejoin="round" />
                </g>
                <defs>
                  <clipPath id="clip0_76_521">
                    <rect width="16" height="16" fill="white" />
                  </clipPath>
                </defs>
              </svg>
            </span>
          </button>
        </div>


        <button class='fpw-font-base fpw-cancel-btn fpw-item-inactive'>Cancel</button>

        <a href="#" class="fpw-font-base fpw-read-more-btn">Read more from our Blog</a>
      </div>

    </main>
  </div>
</div>

<style>
  @import url("https://fonts.googleapis.com/css2?family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&display=swap");

  * {
    margin: 0;
    padding: 0;
    text-decoration: none;
    list-style: none;
    box-sizing: border-box;
  }

  img {
    width: 100%;
    height: auto;
  }

  label,
  span,
  a {
    display: inline-block;
  }

  .fpw-font-base {
    font-size: 12px;
    font-weight: 500;
    font-family: "inter", sans-serif;
    color: #A7A7A7;
  }

  .fpw-bold {
    font-weight: 600;
  }

  .line-height-0 {
    line-height: 0;
  }

  .fpw-margin-bottom-0 {
    margin-bottom: 0 !important;
  }

  .fpw-item-inactive {
    display: none !important;
  }

  .fpw-item-active {
    display: block !important;
  }

  .fp-container {
    max-width: 340px;
    margin: 0 auto;
  }

  .fpw-card-overlay {
    height: 100%;
    width: 100vw;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 999;
    overflow-y: auto;
    padding: 0px;
    background-color: var(--background-primary-color);
    border: 1px solid rgba(255, 255, 255, 0.1019607843);
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .fpw-lowding-box {
    display: flex;
    justify-content: center;
    align-items: center;
    height: auto;
    width: 100%;
    background-color: transparent;
  }

  .fpw-card {
    box-shadow: 2px 2px 6px 0px rgba(0, 0, 0, 0.2);
    background-color: var(--background-primary-color);
    max-width: 100%;
    width: 100%;
    border-radius: 0px;
    /* max-height: 503px; */
    height: 100%;
  }


  .fpw-flex {
    display: flex;
  }

  .fpw-gap-3 {
    gap: 5px;
  }

  .fpw-flex-column {
    flex-direction: column;
  }

  .fpw-align-items-center {
    align-items: center;
  }

  .fpw-justify-content-center {
    justify-content: center;
  }

  .fpw-justify-content-space-between {
    justify-content: space-between;
  }

  .line-height-0 {
    line-height: 0;
  }

  :root {
    --background-primary-color: #2C2C2C;
    --background-secendary-color: #383838;
    --primary-text-color: #FFFFFF;
    --secendary-text-color: #878787;
    --border-color: #404040;
    --text-color-1: #A7A7A7;
    --text-color-2: #C3C3C3;
    --text-color-3: #ECECEC;
  }

  .fpw-main-section .fpw-navigation {
    padding: 8px 16px;
    border-bottom: 1px solid var(--border-color);
  }

  .fpw-main-section .fpw-navigation .fpw-navigation-list .fpw-navigation-item {
    padding: 4px 8px;
    border-radius: 4px;
    margin-left: 4px;
    color: var(--secendary-text-color);
  }

  .fpw-main-section .fpw-navigation .fpw-navigation-list .fpw-navigation-item:first-child {
    margin-left: 0;
  }

  .fpw-main-section .fpw-navigation .fpw-navigation-list .fpw-navigation-item.active-tag {
    color: var(--primary-text-color);
    background-color: var(--background-secendary-color);
  }

  .fpw-main-section .fpw-content {
    padding: 16px 16px 0px 16px;
  }

  .fpw-main-section .fpw-content .fpw-content-title {
    color: var(--primary-text-color);
    margin-bottom: 8px;
  }

  .fpw-main-section .fpw-content .fpw-apikey-wrapper {
    margin-bottom: 24px;
  }

  .fpw-main-section .fpw-content .fpw-apikey-wrapper .fpw-error-notification {
    color: #FD6A6A;
    padding-right: 30px;
    display: none;
  }

  .fpw-main-section .fpw-content .fpw-apikey-wrapper .fpw-apikey-form {
    border: 1px solid #444444;
    background: #383838;
    border-radius: 4px;
    padding: 6px 2px;
    margin-bottom: 8px;
    position: relative;
    overflow: hidden;
  }

  .fpw-main-section .fpw-content .fpw-apikey-wrapper .fpw-apikey-form.fpw-error-field {
    border: 1px solid #FB1313;
    border-radius: 4px;
  }

  .fpw-main-section .fpw-content .fpw-apikey-wrapper .fpw-apikey-form::before {
    content: '';
    position: absolute;
    width: 10px;
    right: 0;
    top: 0;
    bottom: 0;
    height: 100%;
    background-color: #383838;
    box-shadow: -5px 0 4px 0px #383838;
  }

  .fpw-main-section .fpw-content .fpw-apikey-wrapper .fpw-apikey-form .fpw-apikey-pest-field {
    background-color: transparent;
    border: none;
    outline: none;
    width: 100%;
    padding: 2px 6px;
    color: var(--primary-text-color);

  }


  .fpw-main-section .fpw-content .fpw-apikey-wrapper .fpw-apikey-form .fpw-apikey-pest-field::-moz-placeholder {
    color: #888888;
  }

  .fpw-main-section .fpw-content .fpw-apikey-wrapper .fpw-apikey-form .fpw-apikey-pest-field::placeholder {
    color: #888888;
  }

  .fpw-main-section .fpw-content .fpw-apikey-wrapper .fpw-generate-notification {
    color: var(--text-color-1);
    line-height: 1.5;
  }

  .fpw-main-section .fpw-content .fpw-apikey-wrapper .fpw-generate-notification a {
    display: inline;
    color: #1294FF;
    text-decoration: underline;
  }

  .fpw-main-section .fpw-content .fpw-guidelines-wrapper .fpw-guideline-layer-name-list {
    gap: 4px;
    flex-wrap: wrap;
    padding-right: 20px;
    margin-bottom: 32px;
  }

  .fpw-main-section .fpw-content .fpw-guidelines-wrapper .fpw-guideline-layer-name-list .fpw-guideline-layer-name-item {
    color: #ECECEC;
    padding: 4px;
    border-radius: 4px;
    background: rgba(255, 255, 255, 0.04);
  }

  .fpw-main-section .fpw-content .fpw-guidelines-wrapper .fpw-guidelines-description {
    margin-bottom: 16px;
  }

  .fpw-main-section .fpw-content .fpw-guidelines-wrapper .fpw-guidelines-list {
    list-style: auto;
    padding-left: 14px;
    padding-right: 35px;
    margin-bottom: 24px;
  }

  .fpw-main-section .fpw-content .fpw-guidelines-wrapper .fpw-guidelines-list .fpw-guidelines-list-item {
    list-style: auto;
    color: var(--text-color-3);
    margin-bottom: 12px;
    font-weight: 400;
    line-height: 1.2;
  }

  .fpw-main-section .fpw-content .fpw-guidelines-wrapper .fpw-guidelines-list .fpw-guidelines-list-item:last-child {
    margin-bottom: 0;
  }

  .fpw-main-section .fpw-content .fpw-select-layer-wrapper .fpw-img-wrapper {
    width: auto;
    height: auto;
    text-align: center;
    margin-bottom: 0px;
  }

  .fpw-main-section .fpw-content .fpw-select-layer-wrapper .fpw-img-wrapper img {
    width: auto;
    height: 72px;
    -o-object-fit: cover;
    object-fit: cover;
  }

  .fpw-main-section .fpw-content .fpw-select-layer-wrapper .fpw-text-wrapper {
    margin-bottom: 16px;
    text-align: center;
  }

  .fpw-main-section .fpw-content .fpw-select-layer-wrapper .fpw-fig-input-wrapper .fpw-fig-input {
    display: none;
  }

  .fpw-main-section .fpw-content .fpw-select-layer-wrapper .fpw-fig-input-wrapper .fpw-fig-input-custom-field {
    width: 100%;
    text-align: center;
    padding: 24px;
    border: 1px dashed #444444;
    border-radius: 5px;
    color: var(--text-color-3);
    cursor: pointer;
  }

  .fpw-main-section .fpw-content .fpw-select-layer-wrapper .fpw-fig-input-wrapper .fpw-fig-output-custom-field {
    width: 100%;
    text-align: center;
    border: 1px dashed #444444;
    border-radius: 5px;
    color: var(--text-color-3);
    cursor: pointer;
    height: 180px;
  }

  .fpw-main-section .fpw-content .fpw-select-layer-wrapper .fpw-fig-input-wrapper .fpw-fig-output-custom-field img {
    height: 100%;
    width: auto;
    max-width: 100%;
    -o-object-fit: cover;
    object-fit: cover;
  }

  .fpw-main-section .fpw-content .fpw-generate-wrapper .fpw-text-wrapper {
    margin-bottom: 16px;
    text-align: center;
  }

  .fpw-main-section .fpw-content .fpw-generate-wrapper .fpw-generate-load-wrapper {
    border: 1px solid #444444;
    background-color: var(--background-secendary-color);
    border-radius: 8px;
    height: 164px;
    width: 100%;
  }



  .fpw-main-section .fpw-content .fpw-generate-wrapper .fpw-generate-load-wrapper .fpw-generate-load {
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
  }

  .fpw-main-section .fpw-content .fpw-success-wrapper .fpw-img-wrapper {
    width: auto;
    height: auto;
    text-align: center;
    margin-bottom: 0px;
  }

  .fpw-main-section .fpw-content .fpw-success-wrapper .fpw-contract-support-team {
    font-size: 10px;
    text-align: center;
    padding: 16px;
  }

  .fpw-main-section .fpw-content .fpw-success-wrapper .fpw-contract-support-team a {
    color: rgba(0, 124, 226, 1);
    cursor: pointer;
    font-weight: 500;
    text-decoration: underline;
  }

  .fpw-main-section .fpw-content .fpw-success-wrapper .fpw-img-wrapper img {
    width: auto;
    height: 40px;
    -o-object-fit: cover;
    object-fit: cover;
  }

  .fpw-main-section .fpw-content .fpw-success-wrapper .fpw-text-wrapper {
    margin-bottom: 16px;
    text-align: center;
  }

  .fpw-main-section .fpw-content .fpw-success-wrapper .fpw-generate-load-wrapper {
    width: 100%;
  }

  .fpw-main-section .fpw-content .fpw-success-wrapper .fpw-generate-load-wrapper .fpw-generate-load-code {
    display: block;
    border: 1px solid #444444;
    background-color: var(--background-secendary-color);
    border-radius: 8px;
    height: 164px;
    width: 100%;
    overflow-y: auto;
    margin-bottom: 16px;
    padding: 12px 12px 0 12px;
  }

  .fpw-main-section .fpw-content .fpw-success-wrapper .fpw-generate-load-wrapper .fpw-generate-load-code::-webkit-scrollbar {
    width: 8px;
    background-color: #515151;
    overflow: hidden;
    border-top-right-radius: 7px;
    border-bottom-right-radius: 7px;
  }

  .fpw-main-section .fpw-content .fpw-success-wrapper .fpw-generate-load-wrapper .fpw-generate-load-code::-webkit-scrollbar-thumb {
    background-color: #7C7C7C;
    /* Scrollbar thumb */
    border-radius: 2px;
  }

  .fpw-main-section .fpw-content .fpw-success-wrapper .fpw-download-button,
  .fpw-main-section .fpw-content .fpw-success-wrapper .fpw-code-copy-button {
    width: 100%;
    padding: 12px;
    cursor: pointer;
    background-color: #007CE2;
    color: var(--primary-text-color);
    border-radius: 8px;
    border: none;
    outline: none;
    gap: 4px;
    justify-content: center;
    margin-bottom: 5px;
    z-index: 999;
  }

  .fpw-main-section .fpw-content .fpw-success-wrapper .fpw-download-button {
    background-color: transparent;
    border: 1px solid #404040;
    color: #F1F1F1;
    padding: 12.5px 5px;
    margin-bottom: 0px;
  }

  .fpw-main-section .fpw-continue-button-wrapper {
    padding: 8px 16px;
    text-align: center;
  }

  .fpw-main-section .fpw-continue-button-wrapper .fpw-guidelines-support {
    display: flex;
    align-items: center;
    gap: 6px;
    font-weight: 400;
    margin-bottom: 16px;
    max-width: 200px;
    cursor: pointer;
  }

  .fpw-main-section .fpw-continue-button-wrapper .fpw-guidelines-support input[type=checkbox] {
    display: none;
  }

  .fpw-main-section .fpw-continue-button-wrapper .fpw-guidelines-support .support-checkbox {
    width: 14px;
    height: 14px;
    border: 1px solid #4F4F4F;
    overflow: hidden;
    border-radius: 2px;
    cursor: pointer;
    position: relative;
    z-index: -1;
  }

  .fpw-main-section .fpw-continue-button-wrapper .fpw-guidelines-support input[type=checkbox]:checked+.support-checkbox {
    background-color: #007CE2;
  }

  .fpw-main-section .fpw-continue-button-wrapper .fpw-guidelines-support input[type=checkbox]:checked+.support-checkbox::after {
    content: "";
    display: block;
    position: absolute;
    left: 1px;
    top: 1px;
    width: 10px;
    height: 10px;
    background-image: url("data:image/svg+xml,%3Csvg width='8' height='6' viewBox='0 0 8 6' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M1.375 3.375L2.875 4.875L6.625 1.125' stroke='white' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: center;
    background-size: contain;
  }

  .fpw-main-section .fpw-continue-button-wrapper .fpw-continue-button {
    width: 100%;
    padding: 12.5px;
    cursor: pointer;
    background-color: #007CE2;
    color: var(--primary-text-color);
    border-radius: 8px;
    border: none;
    outline: none;
    gap: 4px;
    justify-content: center;
    margin-bottom: 24px;
    z-index: 999;
  }

  .fpw-main-section .fpw-continue-button-wrapper .fpw-read-more-btn {
    text-align: center;
    cursor: pointer;
    color: var(--text-color-2);
  }

  .fpw-main-section .fpw-continue-button-wrapper .fpw-cancel-btn {
    text-align: center;
    color: #C3C3C3;
    background-color: transparent;
    border: none;
    margin: 0 auto;
    cursor: pointer;
    padding: 10px;
  }



  .fpw-button-disabled {
    cursor: not-allowed;
    pointer-events: none !important;
    background-color: #007ce269 !important;
  }

  /*# sourceMappingURL=style.css.map */
</style>

<script>
  /**
  * Figma Plugin Widget Controller
  * Manages the UI interactions and state for a Figma plugin widget
  */
  let loaderTimeout;
  let APIKeySuccess = false;
  let currentActiveTab = 0;
  class FigmaPluginWidget {
    constructor() {
      // DOM Elements
      this.elements = {
        apiKeyInput: document.querySelector('.fpw-apikey-pest-field'),
        fileInput: document.getElementById('figFileInput'),
        fileInputLabel: document.querySelector('.fpw-fig-input-custom-field'),
        navigationItems: document.querySelectorAll('.fpw-navigation-item'),
        continueButton: document.querySelector('.fpw-continue-button'),
        continueButtonWrapper: document.querySelector('.fpw-main-button-wrapper'),
        cancelButton: document.querySelector('.fpw-cancel-btn'),
        readMoreLink: document.querySelector('.fpw-read-more-btn'),
        checkboxForm: document.querySelector('.fpw-guidelines-support-btn'),
        apiKeySection: document.querySelector('.fpw-apikey'),
        guidelinesSection: document.querySelector('.fpw-guidelines'),
        selectLayerSection: document.querySelector('.fpw-select-layer'),
        readyToSelect: document.querySelector('.fpw-fig-output-custom-field'),
        imgWrapper: document.querySelector('.fpw-img-wrapper'),
        beforeGenerateHeader: document.querySelector('.fpw-before-generate-header'),
        afterGenerateHeader: document.querySelector('.fpw-after-generate-header'),
        generateSection: document.querySelector('.fpw-generate'),
        generateSectionHeader: document.querySelector('.fpw-generate .fpw-text-wrapper'),
        generateSectionLoder: document.querySelector('.fpw-generate .fpw-generate-load'),
        completeSection: document.querySelector('.fpw-complete-concert'),
        supportCheckbox: document.querySelector('#ISupport'),
        copyButton: document.querySelector('.fpw-code-copy-button'), // Added
        downloadButton: document.querySelector('.fpw-download-button'),
      };

      // State
      this.state = {
        cancelClicked: false,
        // this.config?.figmaApiKey?.length > 0 ? 1 : 0 // Track the current active tab (0-based index)
      };

      // Initialize
      this.init();

      this.elements.supportCheckbox.checked = false;

    }

    setConfig(config) {
      if (config.type === 'load-settings') {
        this.config = { ...this.config, ...config }
      }
    }

    init() {
      this.setupEventListeners();
    }

    handleApiKeyInputChange() {
      const apiKey = this.elements.apiKeyInput.value.trim();
      const continueButton = this.elements.continueButton;
      if (currentActiveTab == 0) {
        if (apiKey) {
          continueButton.classList.remove('fpw-button-disabled');
          continueButton.disabled = false;
        } else {

          continueButton.classList.add('fpw-button-disabled');
          continueButton.disabled = true;
        }
      }
    }

    setupEventListeners() {
      // File input change handler
      this.elements.fileInput.addEventListener('change', this.handleFileInputChange.bind(this));
      this.elements.apiKeyInput.addEventListener('input', this.handleApiKeyInputChange.bind(this));

      // Navigation item click handler
      this.elements.navigationItems.forEach(item => {
        item.addEventListener('click', this.handleNavigationItemClick.bind(this));
      });

      // Continue button click handler
      document.addEventListener('click', (event) => {
        if (event.target.closest('.fpw-continue-button')) {

          if (this.elements.navigationItems[0].classList.contains('active-tag') && currentActiveTab === 0) {
            this.handleApiKeySave();
          }

          if (!this.elements.navigationItems[0].classList.contains('active-tag') && currentActiveTab === 0) {
            return
          }

          if (APIKeySuccess == true) {
            const activeTab = currentActiveTab
            this.handleButtonContentControl();
            this.handleNavigationControl(activeTab);
          }
        }

        if (event.target.closest('.fpw-cancel-btn')) {
          this.handleCancelClick();
        }

        if (event.target.closest('.support-checkbox')) {
          this.handleCheckbox();
        }

        // Handle "Generate Another File" button click
        if (event.target.closest('.fpw-generate-another-file')) {
          this.handleGenerateAnotherFile();
        }
      });

      const supportCheckbox = document.getElementById('ISupport');
      if (supportCheckbox) {
        supportCheckbox.addEventListener('change', this.handleSupportCheckboxChange.bind(this));
      }
    }

    //Handle API Key Save
    handleApiKeySave() {
      const apiKey = this.elements.apiKeyInput.value.trim();
      if (apiKey) {
        parent.postMessage({
          pluginMessage: {
            type: 'save-api-key',
            figmaApiKey: apiKey
          }
        }, '*');
      }
    }

    // Handle navigation item click
    handleNavigationItemClick(event) {
      const clickedItem = event.currentTarget;
      const clickedTabIndex = parseInt(clickedItem.getAttribute('data-tab-index'));
      const currentActiveTabIndex = currentActiveTab;

      // Only allow clicking on previous tabs or the current tab
      if (clickedTabIndex <= currentActiveTabIndex) {
        // Hide all content sections first
        this.toggleElementVisibility(this.elements.apiKeySection, false);
        this.toggleElementVisibility(this.elements.guidelinesSection, false);
        this.toggleElementVisibility(this.elements.selectLayerSection, false);
        this.toggleElementVisibility(this.elements.generateSection, false);
        this.toggleElementVisibility(this.elements.completeSection, false);

        // Remove active class from all tabs
        this.elements.navigationItems.forEach(item => {
          item.classList.remove('active-tag');
        });


        this.removeActiveTag();
        // Add active class to clicked tab
        clickedItem.classList.add('active-tag');

        // Update the current active tab in the state
        currentActiveTab = clickedTabIndex;

        // Update the UI based on the selected tab
        this.handleButtonContentControl();

        // Make sure the continue button is visible for tabs that need it
        if (clickedTabIndex < 2) {
          this.toggleElementVisibility(this.elements.continueButton, true);
        }

        // Hide the cancel button when not in the Generate tab
        if (clickedTabIndex !== 3) {
          this.toggleElementVisibility(this.elements.cancelButton, false);
        }
      }
    }

    handleSupportCheckboxChange(event) {
      const isChecked = event.target.checked;
      const continueButton = this.elements.continueButton;

      if (currentActiveTab == 1) {
        if (isChecked) {
          continueButton.classList.remove('fpw-button-disabled');
          continueButton.disabled = false;
        } else {
          continueButton.classList.add('fpw-button-disabled');
          continueButton.disabled = true;
        }
      }
    }
    // Handle "Generate Another File" button click
    handleGenerateAnotherFile() {

      // Navigate to the "Select Layer" tab (index 2)
      this.removeActiveTag();

      this.elements.navigationItems[2].classList.add('active-tag');
      currentActiveTab = 2;

      // // Hide the Generate tab content and Complete section
      this.toggleElementVisibility(this.elements.generateSection, false);
      this.toggleElementVisibility(this.elements.completeSection, false);

      // Immediately call the function and don't keep a reference
      (() => {
        this.toggleElementVisibility(this.elements.generateSection, true);
        document.querySelector('.fpw-generate .fpw-content-title').innerHTML = 'Generate Another File'
        document.querySelector('.fpw-generate .fpw-guidelines-description').innerHTML = ''
      })();

      setTimeout(() => {
        this.handleThirdTabActive();
        this.toggleElementVisibility(this.elements.generateSection, false);
      }, 5000);
    }

    // Event Handlers
    handleFileInputChange() {
      const file = this.elements.fileInput.files[0];

      if (file && file.name.endsWith('.fig')) {
        this.elements.fileInputLabel.textContent = `Selected: ${file.name}`;
      } else {
        this.elements.fileInputLabel.textContent = 'Invalid file. Please select a .fig file.';
        this.elements.fileInput.value = '';
      }

      if (this.elements.fileInput.files.length > 0) {
        this.updateUIForFileSelected();
      }
    }

    async handleApiKeyPaste() {
      try {
        const text = await navigator.clipboard.readText();
        this.elements.apiKeyInput.value = text;
      } catch (err) {
        console.error('Failed to read clipboard contents: ', err);
      }
    }

    handleCancelClick() {
      this.state.cancelClicked = true;
      const { navigationItems } = this.elements;

      this.removeActiveTag();

      navigationItems[2].classList.add('active-tag');

      this.toggleElementVisibility(this.elements.generateSection, false);
      this.toggleElementVisibility(this.elements.selectLayerSection, true);
      this.toggleElementVisibility(this.elements.continueButton, true);
      this.toggleElementVisibility(this.elements.cancelButton, false);
    }

    // Navigation Control
    handleNavigationControl(activeIndex) {
      if (!activeIndex) return; // If no active tab found, stop here 

      this.elements.navigationItems[activeIndex].classList.remove('active-tag');
      this.elements.navigationItems[activeIndex + 1].classList.add('active-tag');
    }

    // UI Updates
    handleButtonContentControl() {
      let { navigationItems } = this.elements;
      if (navigationItems[0].classList.contains('active-tag')) {
        this.handleFirstTabActive();
        this.handleApiKeyInputChange();
        this.elements.continueButton.removeAttribute('disabled');

      } else if (navigationItems[1].classList.contains('active-tag')) {
        this.handleSecondTabActive();
        this.resetCheckbox()

      } else if (navigationItems[2].classList.contains('active-tag')) {
        this.handleThirdTabActive();

      } else if (navigationItems[3].classList.contains('active-tag')) {
        this.handleFourthTabActive();
      }
    }

    // leon added 
    // reset checkbox function 
    resetCheckbox() {
      const checkbox = document.getElementById('ISupport');
      if (checkbox) {
        checkbox.checked = false;

        this.elements.continueButton.classList.add('fpw-button-disabled');
        this.elements.continueButton.disabled = true;

        // checkbox.onchange = null;
        // checkbox.addEventListener('change', () => {
        // 	this.elements.continueButton.disabled = !checkbox.checked;
        // 	this.elements.continueButton.classList.toggle('fpw-button-disabled', !checkbox.checked);
        // });
      }
    }

    removeActiveTag() {
      this.elements.navigationItems.forEach(item => item.classList.remove('active-tag'));
    }

    handleFirstTabActive() {
      const apiKey = this.elements.apiKeyInput.value
      if (apiKey.length < 3) {
        alert("API key is invalid!");
        return;
      }
      // Update the current active tab in the state
      currentActiveTab = 0; // First tab (0-based index)

      this.updateContinueButton('Save and Continue');

      this.toggleElementVisibility(this.elements.checkboxForm, false);
      this.toggleElementVisibility(this.elements.cancelButton, false);

      // added leon 
      // all time active readMoreLink in 1st tab 
      // if (this.elements.apiKeyInput.value) {
      this.toggleElementVisibility(this.elements.readMoreLink, true);
      // }

      this.toggleElementVisibility(this.elements.apiKeySection, true);
      this.toggleElementVisibility(this.elements.guidelinesSection, false);
      this.toggleElementVisibility(this.elements.selectLayerSection, false);
    }

    handleSecondTabActive() {
      // Update the current active tab in the state
      currentActiveTab = 1; // Second tab (0-based index)


      this.elements.continueButton.disabled = true;
      this.elements.continueButton.classList.add('fpw-button-disabled');

      const activeBtn = document.querySelector('#ISupport');

      activeBtn.addEventListener('change', (event) => {
        const isChecked = event.target.checked;
        this.elements.continueButton.disabled = !isChecked;
        this.elements.continueButton.classList.toggle('fpw-button-disabled', !isChecked);
      });

      this.updateContinueButton('Continue');
      this.toggleElementVisibility(this.elements.checkboxForm, true);
      this.toggleElementVisibility(this.elements.readMoreLink, false);
      this.toggleElementVisibility(this.elements.apiKeySection, false);
      this.toggleElementVisibility(this.elements.guidelinesSection, true);
      this.toggleElementVisibility(this.elements.selectLayerSection, false);
    }

    handleThirdTabActive() {
      // Update the current active tab in the state
      currentActiveTab = 2; // Third tab (0-based index)

      // Send message to Figma to select a layer
      parent.postMessage({
        pluginMessage: {
          type: 'select-layer'
        }
      }, '*');

      const figOutputField = document.querySelector('.fpw-fig-output-custom-field');

      if (figOutputField) {
        const imgElement = figOutputField.querySelector('img');
        if (!imgElement.src) {
          document.querySelector('.fpw-select-layer .fpw-generate-load-wrapper').classList.add('fpw-item-active')
        }
        // else {
        //   this.toggleElementVisibility(this.elements.continueButton, true);
        //   this.elements.continueButton.continueButton.classList.remove('fpw-button-disabled');
        //   this.elements.continueButton.continueButton.disabled = false;
        // }
      }

      // Set up the default UI state for the third tab
      this.updateContinueButton('Generate Code');
      this.toggleElementVisibility(this.elements.checkboxForm, false);
      // this.toggleElementVisibility(this.elements.continueButton, false);
      this.toggleElementVisibility(this.elements.readMoreLink, false);
      this.toggleElementVisibility(this.elements.cancelButton, false);
      this.toggleElementVisibility(this.elements.apiKeySection, false);
      this.toggleElementVisibility(this.elements.guidelinesSection, false);
      this.toggleElementVisibility(this.elements.selectLayerSection, true);
      document.querySelector('.fpw-generate .fpw-content-title').innerHTML = 'Converting Figma Design to JSON Code'
      document.querySelector('.fpw-generate .fpw-guidelines-description').innerHTML = 'Sit tight! This may take a minute depending on the design complexity. 🙏🏻'

      // Make sure the before-generate header is visible and after-generate header is hidden by default
      const beforeGenerateHeader = document.querySelector('.fpw-before-generate-header');
      const afterGenerateHeader = document.querySelector('.fpw-after-generate-header');
      const figInputWrapper = document.querySelector('.fpw-fig-input-wrapper');

      if (beforeGenerateHeader) {
        beforeGenerateHeader.classList.remove('fpw-item-inactive');
      }

      if (afterGenerateHeader) {
        afterGenerateHeader.classList.add('fpw-item-inactive');
      }

      if (figInputWrapper) {
        figInputWrapper.classList.remove('fpw-item-inactive');
      }
    }

    handleFourthTabActive() {
      // Update the current active tab in the state
      currentActiveTab = 3; // Third tab (0-based index)

      // Trigger the plugin action to create shapes (this will get the JSON data)
      parent.postMessage({
        pluginMessage: {
          type: 'generate-json'
        }
      }, '*');


      this.toggleElementVisibility(this.elements.apiKeySection, false);
      this.toggleElementVisibility(this.elements.guidelinesSection, false);
      this.toggleElementVisibility(this.elements.selectLayerSection, false);
      this.toggleElementVisibility(this.elements.generateSection, true);
      this.toggleElementVisibility(this.elements.continueButton, false);
      this.toggleElementVisibility(this.elements.cancelButton, true);
    }

    // Helper Methods
    updateUIForFileSelected() {
      const {
        continueButtonWrapper,
        continueButton,
        readyToSelect,
        fileInputLabel,
        imgWrapper,
        beforeGenerateHeader,
        afterGenerateHeader
      } = this.elements;

      continueButtonWrapper.classList.add('fpw-item-active');
      continueButton.classList.remove('fpw-item-inactive');
      this.updateContinueButton('Generate Code');
      readyToSelect.classList.remove('fpw-item-inactive');
      readyToSelect.classList.add('fpw-item-active');
      fileInputLabel.classList.add('fpw-item-inactive');
      imgWrapper.classList.add('fpw-item-inactive');
      beforeGenerateHeader.classList.add('fpw-item-inactive');
      afterGenerateHeader.classList.add('fpw-item-active');
    }

    updateContinueButton(text) {
      this.elements.continueButton.innerHTML = `
      <span>${text}</span>
      <span class="line-height-0">
          <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
              <g clip-path="url(#clip0_76_521)">
                  <path d="M5.8401 3.68002L10.1601 8.00002L5.8401 12.32" stroke="white" stroke-width="1.08" stroke-linecap="round" stroke-linejoin="round" />
              </g>
              <defs>
                  <clipPath id="clip0_76_521">
                      <rect width="16" height="16" fill="white" />
                  </clipPath>
              </defs>
          </svg>
      </span>
  `;
    }

    toggleElementVisibility(element, shouldShow) {
      if (shouldShow) {
        element.classList.remove('fpw-item-inactive');
        element.classList.remove('fpw-item-active');
      } else {
        element.classList.remove('fpw-item-active');
        element.classList.add('fpw-item-inactive');
      }
    }
  }

  // Create a reference to the widget instance that will be set when initialized
  let figmaPluginWidgetInstance = null;

  // Listen for messages from the Figma plugin
  onmessage = (event) => {
    const msg = event.data.pluginMessage;

    // // Initialize the widget when DOM is loaded
    figmaPluginWidgetInstance = new FigmaPluginWidget(msg);

    figmaPluginWidgetInstance.setConfig(msg);

    if (msg.type === 'load-settings' && msg.figmaApiKey) {
      APIKeySuccess = true;
      document.getElementById('fpw-figma-api-key').value = msg.figmaApiKey;

      const continueButton = document.querySelector('.fpw-continue-button');
      if (continueButton && figmaPluginWidgetInstance.state.currentActiveTab == 0) {
        continueButton.classList.remove('fpw-button-disabled');
        continueButton.disabled = false;
      }
      // leon 
      // Get the navigation items
      figmaPluginWidgetInstance.removeActiveTag();
      // Add active class to Guidelines tab
      figmaPluginWidgetInstance.elements.navigationItems[1].classList.add('active-tag');
      // Update the current active tab in the state
      // currentActiveTab = 3;
      // Update the UI
      figmaPluginWidgetInstance.handleSecondTabActive();
      // figmaPluginWidgetInstance.handleThirdTabActive();
    }

    if (msg.type === 'api-key-error') {
      const apiKeyFildError = document.querySelector('.fpw-apikey-form')
      const apiKeyTextError = document.querySelector('.fpw-error-notification')
      const generateNotifications = document.querySelectorAll('.fpw-generate-notification')
      apiKeyFildError.classList.add('fpw-error-field')
      apiKeyTextError.classList.add('fpw-item-active')
      generateNotifications.forEach((generateNotification) => {
        generateNotification.classList.add('fpw-item-inactive')
      })
    }
    if (msg.type === 'api-key-success') {
      // Get the navigation items
      figmaPluginWidgetInstance.removeActiveTag();
      // Add active class to Guidelines tab
      figmaPluginWidgetInstance.elements.navigationItems[1].classList.add('active-tag');
      // Update the current active tab in the state
      // currentActiveTab = 1;
      // Update the UI
      figmaPluginWidgetInstance.handleSecondTabActive();
      // figmaPluginWidgetInstance.handleButtonContentControl();

      const apiKeyFildError = document.querySelector('.fpw-apikey-form')
      const apiKeyTextError = document.querySelector('.fpw-error-notification')
      const generateNotifications = document.querySelectorAll('.fpw-generate-notification')

      apiKeyFildError.classList.remove('fpw-error-field')
      apiKeyTextError.classList.remove('fpw-item-active')
      generateNotifications.forEach((generateNotification) => {
        generateNotification.classList.remove('fpw-item-inactive');
      });

      APIKeySuccess = true
    }

    // Check if we have an instance and if we're on the third tab
    if (currentActiveTab === 2) {
      // Handle layer selection message
      if (msg.type === 'layer-selected' && msg.data) {

        // Get the elements we need to manipulate
        const beforeGenerateHeader = document.querySelector('.fpw-before-generate-header');
        const afterGenerateHeader = document.querySelector('.fpw-after-generate-header');
        const figInputWrapper = document.querySelector('.fpw-fig-input-wrapper');
        const figOutputField = document.querySelector('.fpw-fig-output-custom-field');
        const figLoader = document.querySelector('.fpw-select-layer .fpw-generate-load-wrapper');

        if (msg.data && msg.processing) {
          figLoader.classList.remove('fpw-item-inactive');
          figLoader.classList.add('fpw-item-active')
        }

        // Hide the before generate header and fig input form
        if (beforeGenerateHeader) {
          beforeGenerateHeader.classList.add('fpw-item-inactive');
        }

        if (figInputWrapper) {
          // Don't hide the entire wrapper, just the input form
          const inputLabel = figInputWrapper.querySelector('.fpw-fig-input-custom-field');
          if (inputLabel) {
            inputLabel.classList.add('fpw-item-inactive');
          }
        }

        // Show the after generate header and update its content
        if (afterGenerateHeader) {
          afterGenerateHeader.classList.remove('fpw-item-inactive');
          afterGenerateHeader.classList.add('fpw-item-active');

          // Update the title with the selected layer name
          const titleElement = afterGenerateHeader.querySelector('.fpw-after-generate-header .fpw-guidelines-description');
          if (titleElement) {
            titleElement.innerHTML = `You've successfully selected <strong>${msg.data}</strong> to work with`;
          }
        }

        // Show the image if available
        if (msg.imageUrl && figOutputField) {
          figOutputField.classList.remove('fpw-item-inactive');
          figOutputField.classList.add('fpw-item-active');

          // Update the image source
          const imgElement = figOutputField.querySelector('img');



          if (imgElement) {
            imgElement.src = msg.imageUrl;
            imgElement.alt = msg.data; // Use the layer name as alt text
            figLoader.classList.remove('fpw-item-active')
            figLoader.classList.add('fpw-item-inactive')
          }
        }
        else if (msg.error) {
          figOutputField.classList.remove('fpw-item-inactive');
          figOutputField.classList.add('fpw-item-active');

          const titleElement = afterGenerateHeader.querySelector('.fpw-after-generate-header .fpw-guidelines-description');
          if (titleElement) {
            titleElement.textContent = `Error: ${msg.error}`;
          }
          const loader = document.querySelector('.fpw-generate')
          loader.classList.add('fpw-item-active')
          loader.classList.remove('fpw-item-inactive')
          // Update the image source
          const imgElement = figOutputField.querySelector('img');
          if (imgElement) {
            imgElement.src = '';
            imgElement.alt = '';
          }
        }

        // Enable the continue button
        if (figmaPluginWidgetInstance.elements.continueButton) {
          console.log('continue button', figmaPluginWidgetInstance.elements.continueButton)
          figmaPluginWidgetInstance.toggleElementVisibility(figmaPluginWidgetInstance.elements.continueButton, true);
        }
      }
    }

    //Handle json data response
    if (currentActiveTab === 3 && msg.type === 'jsonData') {
      if (!msg.data) {
        return;
      }
      if (msg.data && typeof msg.data === 'object') {
        const jsonOutput = JSON.stringify(msg.data, null, 2)

        // Use figmaPluginWidgetInstance instead of this
        figmaPluginWidgetInstance.toggleElementVisibility(figmaPluginWidgetInstance.elements.generateSection, false);
        figmaPluginWidgetInstance.toggleElementVisibility(figmaPluginWidgetInstance.elements.cancelButton, false);
        figmaPluginWidgetInstance.toggleElementVisibility(figmaPluginWidgetInstance.elements.completeSection, true);
        const codeElement = document.querySelector('#code-block');
        if (codeElement) {
          codeElement.textContent = jsonOutput;
        }

        // Copy the JSON content to clipboard after it's rendered
        copyToClipboard(jsonOutput);

        document.getElementById('fwp-json-copy').onclick = () => {
          copyToClipboard(jsonOutput);
        }

        document.getElementById('fwp-json-download').onclick = () => {
          downloadJson(jsonOutput);
        }
      }
    }
  }

  // Initialize the widget when DOM is loaded
  document.addEventListener('DOMContentLoaded', () => {
    figmaPluginWidgetInstance = new FigmaPluginWidget();

    // Check if API key exists and open Guidelines tab by default if it does
    const apiKey = document.getElementById('fpw-figma-api-key').value;

    if (apiKey && apiKey.trim() !== '' && apiKey !== 'api') {
      // Get the navigation items
      const navigationItems = document.querySelectorAll('.fpw-navigation-item');

      // Remove active class from API Key tab
      navigationItems[0].classList.remove('active-tag');

      // Add active class to Guidelines tab
      navigationItems[1].classList.add('active-tag');

      // Update the current active tab in the state
      currentActiveTab = 1;

      // Update the UI
      figmaPluginWidgetInstance.handleSecondTabActive();
    }
  });

  // Copy JSON content to clipboard with fallback method
  function copyToClipboard(text) {
    // Try using Clipboard API first
    if (navigator.clipboard && navigator.clipboard.writeText) {
      navigator.clipboard.writeText(text).then(() => {
        notifyMessage('JSON copy to Clipboard.')
      }).catch((err) => {
        console.error("Failed to copy with Clipboard API:", err);
        fallbackCopyToClipboard(text);
      });
    } else {
      // Fallback using document.execCommand
      fallbackCopyToClipboard(text);
    }
  }

  // Fallback method using document.execCommand
  function fallbackCopyToClipboard(text) {
    const textarea = document.createElement('textarea');
    textarea.value = text;
    document.body.appendChild(textarea);
    textarea.select();
    try {
      const successful = document.execCommand('copy');
      if (successful) {
        notifyMessage('JSON copy to Clipboard.')
      } else {
        // notifyMessage('Failed to copy JSON.')
      }
    } catch (err) {
      console.error("Failed to copy with execCommand:", err);
    }
    document.body.removeChild(textarea);
  }

  function downloadJson(jsonOutput) {
    if (!jsonOutput) {
      alert("Failed to Download JSON.");
      return;
    }
    const blob = new Blob([jsonOutput], { type: "application/json" });
    const url = URL.createObjectURL(blob);

    const anchor = document.createElement("a");
    anchor.href = url;
    anchor.download = "figma-to-wp-for-elementor.json"; // File name
    anchor.click();

    URL.revokeObjectURL(url); // Clean up
  }

  function notifyMessage(msg) {
    parent.postMessage({
      pluginMessage: {
        type: 'notify-message',
        message: msg
      }
    }, '*');
  }

</script>