{
    "content": [
      {
        "id": "2aaf4deb",
        "settings": {
          "flex_direction": "column",
          "width": {
            "unit": "px",
            "size": 397,
            "sizes": []
          },
          "padding": {
            "unit": "px",
            "top": "0",
            "right": "0",
            "bottom": "0",
            "left": "0",
            "isLinked": true
          },
          "height": "min-height",
          "custom_height": {
            "unit": "px",
            "size": 178,
            "sizes": []
          }
        },
        "elements": [
          {
            "id": "7bf4d806",
            "settings": {
              "eael_feature_list": [
                {
                  "eael_feature_list_icon_new": {
                    "value": "fas fa-check",
                    "library": "fa-solid"
                  },
                  // "eael_feature_list_title": "Feature Item 1",
                  // "eael_feature_list_content": "",
                  // "_id": "dece6ce",
                  // "eael_feature_list_icon_type": "image",
                  // "eael_feature_list_img": {
                  //   "url": "http://essential-addons-dev.test/wp-content/uploads/2025/03/Vector.png",
                  //   "id": 20345,
                  //   "size": "",
                  //   "alt": "",
                  //   "source": "library"
                  // }
                },
                // {
                //   "eael_feature_list_title": "Feature Item 2",
                //   "eael_feature_list_content": "",
                //   "_id": "078024a",
                //   "eael_feature_list_icon_type": "image",
                //   "eael_feature_list_img": {
                //     "url": "http://essential-addons-dev.test/wp-content/uploads/2025/03/Vector.png",
                //     "id": 20345,
                //     "size": "",
                //     "alt": "",
                //     "source": "library"
                //   }
                // },
                // {
                //   "eael_feature_list_title": "Feature Item 3",
                //   "eael_feature_list_content": "",
                //   "_id": "c4c0be3",
                //   "eael_feature_list_icon_type": "image",
                //   "eael_feature_list_img": {
                //     "url": "http://essential-addons-dev.test/wp-content/uploads/2025/03/Vector.png",
                //     "id": 20345,
                //     "size": "",
                //     "alt": "",
                //     "source": "library"
                //   }
                // }
              ],
            //   "eael_feature_list_title_size": "p",
            //   "eael_feature_list_connector": "",
            //   "eael_feature_list_space_between": {
            //     "unit": "px",
            //     "size": 0,
            //     "sizes": []
            //   },
            //   "eael_feature_list_icon_background_background": "classic",
            //   "eael_feature_list_icon_background_color": "#FFFFFF",
            //   "eael_feature_list_icon_color": "",
            //   "eael_feature_list_icon_circle_size": {
            //     "unit": "px",
            //     "size": 17,
            //     "sizes": []
            //   },
            //   "eael_feature_list_icon_size": {
            //     "unit": "px",
            //     "size": 17,
            //     "sizes": []
            //   },
            //   "eael_feature_list_icon_padding": {
            //     "unit": "px",
            //     "top": "0",
            //     "right": "0",
            //     "bottom": "0",
            //     "left": "0",
            //     "isLinked": true
            //   },
            //   "eael_feature_list_icon_space": {
            //     "unit": "px",
            //     "size": 8,
            //     "sizes": []
            //   },
            //   "eael_feature_list_title_bottom_space": {
            //     "unit": "px",
            //     "size": 0,
            //     "sizes": []
            //   },
            //   "eael_feature_list_title_color": "#091439",
            //   "eael_feature_list_title_typography_typography": "custom",
            //   "eael_feature_list_title_typography_font_family": "Inter Tight",
            //   "eael_feature_list_title_typography_font_size": {
            //     "unit": "px",
            //     "size": 18,
            //     "sizes": []
            //   },
            //   "eael_feature_list_title_typography_font_weight": "400"
            },
            "elements": [],
            "isInner": false,
            "widgetType": "eael-feature-list",
            "elType": "widget"
          }
        ],
        "isInner": false,
        "elType": "container"
      }
    ],
    "page_settings": {
      "eael_ext_toc_title": "Table of Contents",
      "scroll_viewport": "#outer-wrap",
      "scroll_contentScroll": "#wrap",
      "eael_ext_scroll_to_top": "yes"
    },
    "version": "0.4",
    "title": "okk",
    "type": "container"
  }