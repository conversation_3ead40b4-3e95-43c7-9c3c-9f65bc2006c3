{
    "content": [
      {
        "id": "7072b135",
        "settings": {
          "flex_direction": "column",
          "width": {
            "unit": "px",
            "size": 662,
            "sizes": []
          },
          "padding": {
            "unit": "px",
            "top": "0",
            "right": "0",
            "bottom": "0",
            "left": "0",
            "isLinked": true
          },
          "height": "min-height",
          "custom_height": {
            "unit": "px",
            "size": 313,
            "sizes": []
          }
        },
        "elements": [
          {
            "id": "5da293e5",
            "settings": {
            //   "eael_cta_title": "Empowering Brands with Creative Digital Solutions",
            //   "eael_cta_title_typography_typography": "custom",
            //   "eael_cta_title_typography_font_size": {
            //     "unit": "px",
            //     "size": 64,
            //     "sizes": []
            //   },
            //   "eael_cta_title_typography_line_height": {
            //     "unit": "%",
            //     "size": 120.00000476837158,
            //     "sizes": []
            //   },
            //   "eael_cta_title_typography_text_transform": "none",
            //   "eael_cta_title_typography_letter_spacing": {
            //     "unit": "px",
            //     "size": {
            //       "unit": "PERCENT",
            //       "value": 0
            //     },
            //     "sizes": []
            //   },
            //   "eael_cta_btn_text": "Get Started",
              // "eael_cta_secondary_btn_text": "Book a Call",
            //   "eael_cta_btn_typography_typography": "custom",
            //   "eael_cta_btn_typography_font_family": "Inter Tight",
            //   "eael_cta_btn_typography_font_weight": "500",
            //   "eael_cta_btn_typography_font_size": {
            //     "unit": "px",
            //     "size": 16,
            //     "sizes": []
            //   },
            //   "eael_cta_btn_typography_line_height": {
            //     "unit": "%",
            //     "size": 110.00000238418579,
            //     "sizes": []
            //   },
            //   "eael_cta_btn_typography_text_transform": "none",
            //   "eael_cta_btn_typography_letter_spacing": {
            //     "unit": "px",
            //     "size": {
            //       "unit": "PERCENT",
            //       "value": 0
            //     },
            //     "sizes": []
            //   },
            //   "eael_cta_btn_normal_text_color": "#ffffff",
            //   "eael_cta_btn_hover_text_color": "",
            //   "eael_cat_btn_normal_border_width": {
            //     "unit": "px",
            //     "top": "1",
            //     "right": "1",
            //     "bottom": "1",
            //     "left": "1",
            //     "isLinked": true
            //   },
            //   "eael_cat_btn_normal_border_border": "solid",
            //   "eael_cta_border_width": {
            //     "unit": "px",
            //     "top": "1",
            //     "right": "1",
            //     "bottom": "1",
            //     "left": "1",
            //     "isLinked": true
            //   },
            //   "eael_cta_border_border": "solid",
              "eael_cta_bg_image": {
                "id": "",
                "url": "https://essential-addons-dev.test/wp-content/plugins/elementor/assets/images/placeholder.png"
              },
              // "eael_cta_content": "",
              // "eael_cta_secondary_btn_is_show": "yes",
            //   "eael_cta_btn_normal_bg_color": "#091439",
            //   "eael_cta_btn_hover_bg_color": "#091439",
              // "eael_cta_secondary_btn_normal_bg_color_background": "classic",
              // "eael_cta_bg_color": "#FFFFFF",
              // "eael_cta_container_padding": {
              //   "unit": "px",
              //   "top": "0",
              //   "right": "0",
              //   "bottom": "0",
              //   "left": "0",
              //   "isLinked": true
              // },
              // "eael_cta_container_margin": {
              //   "unit": "px",
              //   "top": "0",
              //   "right": "0",
              //   "bottom": "0",
              //   "left": "0",
              //   "isLinked": true
              // },
              // "eael_cta_title_margin": {
              //   "unit": "px",
              //   "top": "0",
              //   "right": "0",
              //   "bottom": "0",
              //   "left": "0",
              //   "isLinked": true
              // },
              // "eael_cta_secondary_btn_normal_text_color": "#091439",
              // "eael_cta_secondary_btn_normal_bg_color_color": "#FFFFFF",
              // "eael_cat_secondary_btn_normal_border_border": "none",
              // "eael_cta_secondary_btn_hover_text_color": "#091439",
              "eael_cta_secondary_button_shadow_box_shadow_type": "yes",
              "eael_cta_secondary_button_shadow_box_shadow": {
                "horizontal": 0,
                "vertical": 0,
                "blur": 0,
                "spread": 0,
                "color": "rgba(0,0,0,0.5)"
              }
            },
            "elements": [],
            "isInner": false,
            "widgetType": "eael-cta-box",
            "elType": "widget"
          }
        ],
        "isInner": false,
        "elType": "container"
      }
    ],
    "page_settings": {
      "eael_ext_toc_title": "Table of Contents",
      "scroll_viewport": "#outer-wrap",
      "scroll_contentScroll": "#wrap",
      "eael_ext_scroll_to_top": "yes"
    },
    "version": "0.4",
    "title": "cta-box",
    "type": "container"
  }


// eael_cta_secondary_btn_hover_bg_color