{"content": [{"id": "429:1736", "settings": {"flex_direction": "column", "content_width": "boxed", "width": {"unit": "px", "size": 1336, "sizes": []}, "padding": {"unit": "px", "top": "0", "right": "0", "bottom": "0", "left": "0", "isLinked": true}, "height": "min-height", "custom_height": {"unit": "px", "size": 8411, "sizes": []}}, "elements": [{"id": "429:1714", "settings": {"flex_direction": "row", "content_width": "boxed", "width": {"unit": "px", "size": 1320, "sizes": []}, "flex_justify_content": "space-between", "padding": {"unit": "px", "top": "16", "right": "24", "bottom": "16", "left": "24", "isLinked": false}, "background_background": "classic", "background_color": "#ffffff", "border_radius": {"unit": "px", "top": "8", "right": "8", "bottom": "8", "left": "8", "isLinked": true}, "height": "min-height", "custom_height": {"unit": "px", "size": 70, "sizes": []}}, "elements": [{"id": "429:1715", "settings": {"eael_dch_first_title": "FlexiGency", "eael_show_dch_icon_content": "", "eael_dch_last_title": "", "eael_dch_subtext": "", "eael_dch_first_title_typography_typography": "custom", "eael_dch_first_title_typography_font_family": "Inter Tight", "eael_dch_first_title_typography_font_weight": "600", "eael_dch_first_title_typography_font_size": {"unit": "px", "size": 20, "sizes": []}, "eael_dch_first_title_typography_line_height": {"unit": "%", "size": 160.0000023841858, "sizes": []}, "eael_dch_first_title_typography_text_transform": "none", "eael_dch_first_title_typography_letter_spacing": {"unit": "PERCENT", "size": 0, "sizes": []}, "eael_dch_base_title_color": "#091439", "eael_dch_dual_title_color": "#091439", "eael_dch_container_margin": {"unit": "px", "top": "0", "right": "0", "bottom": "0", "left": "0", "isLinked": false}, "typography_typography": "custom", "typography_font_family": "Inter Tight", "typography_font_weight": "600", "typography_font_size": {"unit": "px", "size": 20, "sizes": []}, "typography_line_height": {"unit": "%", "size": 160.0000023841858, "sizes": []}, "title_color": "#091439", "height": "min-height", "custom_height": {"unit": "px", "size": 32, "sizes": []}}, "elements": [], "isInner": false, "elType": "widget", "widgetType": "eael-dual-color-header"}, {"id": "429:1716", "settings": {"eael_advanced_menu_menu": "67", "default_eael_advanced_menu_background": "#ffffff", "default_eael_advanced_menu_item_background_hover": "#ffffff", "default_eael_advanced_menu_item_color": "#5c5f70", "default_eael_advanced_menu_item_indicator_color": "#5c5f70", "default_eael_advanced_menu_dropdown_item_color": "#5c5f70", "default_eael_advanced_menu_dropdown_item_indicator_color": "#5c5f70", "default_eael_advanced_menu_item_typography_typography": "custom", "default_eael_advanced_menu_item_typography_font_family": "Inter Tight", "default_eael_advanced_menu_item_typography_font_weight": "400", "default_eael_advanced_menu_item_typography_font_size": {"unit": "px", "size": 16, "sizes": []}, "default_eael_advanced_menu_item_typography_line_height": {"unit": "%", "size": 160.0000023841858, "sizes": []}, "default_eael_advanced_menu_item_typography_text_transform": "none", "default_eael_advanced_menu_item_typography_letter_spacing": {"unit": "PERCENT", "size": 0, "sizes": []}, "default_eael_advanced_menu_item_typography_line_height_widescreen": {"unit": "%", "size": 160.0000023841858, "sizes": []}, "default_eael_advanced_menu_item_typography_line_height_laptop": {"unit": "%", "size": 160.0000023841858, "sizes": []}, "default_eael_advanced_menu_item_typography_line_height_tablet_extra": {"unit": "%", "size": 160.0000023841858, "sizes": []}, "default_eael_advanced_menu_item_typography_line_height_tablet": {"unit": "%", "size": 160.0000023841858, "sizes": []}, "default_eael_advanced_menu_item_color_hover": "#091439", "default_eael_advanced_menu_item_indicator_color_hover": "#091439", "default_eael_advanced_menu_dropdown_item_color_hover": "#091439", "default_eael_advanced_menu_dropdown_item_indicator_color_hover": "#091439", "default_eael_advanced_menu_layout": "horizontal", "typography_typography": "custom", "background_background": "classic", "background_color": "#ffffff", "height": "min-height", "custom_height": {"unit": "px", "size": 26, "sizes": []}}, "elements": [], "isInner": false, "elType": "widget", "widgetType": "eael-advanced-menu"}, {"id": "429:1731", "settings": {"creative_button_text": "Get Started", "eael_creative_button_typography_typography": "custom", "eael_creative_button_typography_font_family": "Inter Tight", "eael_creative_button_typography_font_weight": "500", "eael_creative_button_typography_font_size": {"unit": "px", "size": 14, "sizes": []}, "eael_creative_button_typography_line_height": {"unit": "%", "size": 110.00000238418579, "sizes": []}, "eael_creative_button_typography_text_transform": "none", "eael_creative_button_typography_letter_spacing": {"unit": "PERCENT", "size": 0, "sizes": []}, "eael_creative_button_text_color": "#ffffff", "eael_creative_button_hover_text_color": "#ffffff", "eael_creative_button_icon_color": "#ffffff", "eael_creative_button_hover_icon_color": "#ffffff", "margin": {"unit": "px", "top": "0", "right": "10", "bottom": "0", "left": "0", "isLinked": false}, "eael_creative_button_icon_alignment": "left", "eael_creative_button_background_color": "#313c5e", "eael_creative_button_hover_background_color": "#313c5e", "eael_creative_button_border_radius": {"unit": "px", "size": "4", "sizes": []}, "typography_typography": "custom", "background_background": "gradient", "background_color": "#313c5e", "background_color_b": "#313c5e", "height": "min-height", "custom_height": {"unit": "px", "size": 39, "sizes": []}}, "elements": [], "isInner": false, "elType": "widget", "widgetType": "eael-creative-button"}], "isInner": false, "elType": "container"}, {"id": "429:2219", "settings": {"flex_direction": "column", "content_width": "boxed", "width": {"unit": "px", "size": 662, "sizes": []}, "padding": {"unit": "px", "top": "0", "right": "0", "bottom": "0", "left": "0", "isLinked": true}, "height": "min-height", "custom_height": {"unit": "px", "size": 313, "sizes": []}}, "elements": [{"id": "429:2220", "settings": {"eael_cta_container_padding": {"unit": "px", "top": "0", "right": "0", "bottom": "0", "left": "0", "isLinked": true}, "eael_cta_container_margin": {"unit": "px", "top": "0", "right": "0", "bottom": "0", "left": "0", "isLinked": false}, "eael_cta_title": "Empowering Brands With Creative Digital Solutions", "eael_cta_title_typography_typography": "custom", "eael_cta_title_typography_font_size": {"unit": "px", "size": 64, "sizes": []}, "eael_cta_title_typography_line_height": {"unit": "%", "size": 120.00000476837158, "sizes": []}, "eael_cta_title_typography_text_transform": "none", "eael_cta_title_typography_letter_spacing": {"unit": "PERCENT", "size": 0, "sizes": []}, "eael_cta_title_margin": {"unit": "px", "top": "0", "right": "0", "bottom": "24", "left": "0", "isLinked": false}, "eael_cta_content": "", "eael_cta_btn_text": "Get Started", "eael_cta_btn_typography_typography": "custom", "eael_cta_btn_typography_font_family": "Inter Tight", "eael_cta_btn_typography_font_weight": "500", "eael_cta_btn_typography_font_size": {"unit": "px", "size": 16, "sizes": []}, "eael_cta_btn_typography_line_height": {"unit": "%", "size": 110.00000238418579, "sizes": []}, "eael_cta_btn_typography_text_transform": "none", "eael_cta_btn_typography_letter_spacing": {"unit": "PERCENT", "size": 0, "sizes": []}, "eael_cta_btn_normal_text_color": "#ffffff", "eael_cta_btn_hover_text_color": "", "eael_cta_btn_normal_bg_color": "#313c5e", "eael_cta_btn_hover_bg_color": "#313c5e", "padding": {"unit": "px", "top": "16", "right": "24", "bottom": "16", "left": "24", "isLinked": false}, "eael_cta_btn_border_radius": {"unit": "px", "size": "4", "sizes": []}, "eael_cat_btn_normal_border_width": {"unit": "px", "top": "2", "right": "2", "bottom": "2", "left": "2", "isLinked": true}, "eael_cat_btn_normal_border_border": "solid", "eael_cat_secondary_btn_normal_border_border": "none", "eael_cta_secondary_btn_is_show": "yes", "eael_cta_secondary_btn_text": "Book a Call", "eael_cta_secondary_btn_typography_typography": "custom", "eael_cta_secondary_btn_typography_font_family": "Inter Tight", "eael_cta_secondary_btn_typography_font_weight": "500", "eael_cta_secondary_btn_typography_font_size": {"unit": "px", "size": 16, "sizes": []}, "eael_cta_secondary_btn_typography_line_height": {"unit": "%", "size": 110.00000238418579, "sizes": []}, "eael_cta_secondary_btn_typography_text_transform": "none", "eael_cta_secondary_btn_typography_letter_spacing": {"unit": "PERCENT", "size": 0, "sizes": []}, "eael_cta_secondary_btn_normal_text_color": "#091439", "eael_cta_secondary_btn_hover_text_color": "#091439", "eael_cta_secondary_btn_normal_bg_color_color": "#ffffff", "eael_cta_secondary_btn_normal_bg_color_background": "classic", "eael_cta_secondary_btn_hover_bg_color_background": "classic", "eael_cta_secondary_btn_hover_bg_color_color": "#ffffff", "eael_cta_border_width": {"unit": "px", "top": "1", "right": "1", "bottom": "1", "left": "1", "isLinked": true}, "eael_cta_border_border": "solid"}, "elements": [], "isInner": false, "elType": "widget", "widgetType": "eael-cta-box"}], "isInner": false, "elType": "container"}, {"id": "429:1737", "settings": {"flex_direction": "column", "content_width": "boxed", "width": {"unit": "px", "size": 1336, "sizes": []}, "padding": {"unit": "px", "top": "32", "right": "32", "bottom": "32", "left": "32", "isLinked": true}, "background_background": "classic", "background_color": "#f4e4fe", "border_radius": {"unit": "px", "top": "16", "right": "16", "bottom": "16", "left": "16", "isLinked": true}, "height": "min-height", "custom_height": {"unit": "px", "size": 626, "sizes": []}}, "elements": [{"id": "429:1738", "settings": {"flex_direction": "row", "content_width": "boxed", "width": {"unit": "px", "size": 1263, "sizes": []}, "padding": {"unit": "px", "top": "0", "right": "0", "bottom": "0", "left": "0", "isLinked": true}, "height": "min-height", "custom_height": {"unit": "px", "size": 562, "sizes": []}}, "elements": [{"id": "429:1739", "settings": {"flex_direction": "column", "content_width": "boxed", "width": {"unit": "px", "size": 707, "sizes": []}, "padding": {"unit": "px", "top": "0", "right": "0", "bottom": "0", "left": "0", "isLinked": true}, "height": "min-height", "custom_height": {"unit": "px", "size": 344, "sizes": []}}, "elements": [{"id": "429:1760", "settings": {"flex_direction": "column", "content_width": "boxed", "width": {"unit": "px", "size": 707, "sizes": []}, "padding": {"unit": "px", "top": "0", "right": "0", "bottom": "0", "left": "0", "isLinked": true}}, "elements": [{"id": "429:1761", "settings": {"flex_direction": "row", "content_width": "boxed", "width": {"unit": "px", "size": 707, "sizes": []}, "padding": {"unit": "px", "top": "0", "right": "0", "bottom": "0", "left": "0", "isLinked": true}, "height": "min-height", "custom_height": {"unit": "px", "size": 160, "sizes": []}}, "elements": [{"id": "429:1762", "settings": {"i": {"unit": "px", "top": "16", "right": "16", "bottom": "16", "left": "16", "isLinked": true}, "_background_color": "#ffffff", "o": "classic", "ending_number": "20", "number_suffix": "+", "counter_num_typography_typography": "custom", "counter_num_typography_font_family": "Inter Tight", "counter_num_typography_font_weight": "600", "counter_num_typography_font_size": {"unit": "px", "size": 48, "sizes": []}, "counter_num_typography_line_height": {"unit": "%", "size": 120.00000476837158, "sizes": []}, "counter_num_typography_text_transform": "none", "counter_num_typography_letter_spacing": {"unit": "PERCENT", "size": 0, "sizes": []}, "section_number_suffix_typography_typography": "custom", "section_number_suffix_typography_font_family": "Inter Tight", "section_number_suffix_typography_font_weight": "600", "section_number_suffix_typography_font_size": {"unit": "px", "size": 48, "sizes": []}, "section_number_suffix_typography_line_height": {"unit": "%", "size": 120.00000476837158, "sizes": []}, "section_number_suffix_typography_text_transform": "none", "section_number_suffix_typography_letter_spacing": {"unit": "PERCENT", "size": 0, "sizes": []}, "counter_num_color": "#091439", "section_number_suffix_color": "#091439", "counter_title": "Years Of Experience", "counter_title_typography_typography": "custom", "counter_title_typography_font_family": "Inter Tight", "counter_title_typography_font_weight": "500", "counter_title_typography_font_size": {"unit": "px", "size": 18, "sizes": []}, "counter_title_typography_line_height": {"unit": "%", "size": 120.00000476837158, "sizes": []}, "counter_title_typography_text_transform": "none", "counter_title_typography_letter_spacing": {"unit": "PERCENT", "size": 0, "sizes": []}, "counter_title_color": "#4c4f59", "margin": {"unit": "px", "top": "0", "right": "0", "bottom": "16", "left": "0", "isLinked": false}, "counter_icon_rotation": {"unit": "deg", "size": -240, "sizes": []}, "counter_icon_new": {"value": "fas fa-long-arrow-alt-right", "library": "fa-solid"}, "counter_icon_color": "", "counter_layout": "layout-6", "eael_icon_type": "icon", "counter_icon_size": {"unit": "px", "size": 50, "sizes": []}, "counter_icon_margin": {"unit": "px", "top": "0", "right": "104", "bottom": "0", "left": "0", "isLinked": false}, "counter_icon_padding": {"unit": "px", "top": "6", "right": "6", "bottom": "6", "left": "6", "isLinked": true}, "typography_typography": "custom", "background_background": "classic", "background_color": "#ffffff"}, "elements": [], "isInner": false, "elType": "widget", "widgetType": "eael-counter"}, {"id": "429:1774", "settings": {"i": {"unit": "px", "top": "16", "right": "16", "bottom": "16", "left": "16", "isLinked": true}, "_background_color": "#ffffff", "o": "classic", "ending_number": "4", "number_suffix": "K", "counter_num_typography_typography": "custom", "counter_num_typography_font_family": "Inter Tight", "counter_num_typography_font_weight": "600", "counter_num_typography_font_size": {"unit": "px", "size": 48, "sizes": []}, "counter_num_typography_line_height": {"unit": "%", "size": 120.00000476837158, "sizes": []}, "counter_num_typography_text_transform": "none", "counter_num_typography_letter_spacing": {"unit": "PERCENT", "size": 0, "sizes": []}, "section_number_suffix_typography_typography": "custom", "section_number_suffix_typography_font_family": "Inter Tight", "section_number_suffix_typography_font_weight": "600", "section_number_suffix_typography_font_size": {"unit": "px", "size": 48, "sizes": []}, "section_number_suffix_typography_line_height": {"unit": "%", "size": 120.00000476837158, "sizes": []}, "section_number_suffix_typography_text_transform": "none", "section_number_suffix_typography_letter_spacing": {"unit": "PERCENT", "size": 0, "sizes": []}, "counter_num_color": "#091439", "section_number_suffix_color": "#091439", "counter_title": "Project Done", "counter_title_typography_typography": "custom", "counter_title_typography_font_family": "Inter Tight", "counter_title_typography_font_weight": "500", "counter_title_typography_font_size": {"unit": "px", "size": 18, "sizes": []}, "counter_title_typography_line_height": {"unit": "%", "size": 120.00000476837158, "sizes": []}, "counter_title_typography_text_transform": "none", "counter_title_typography_letter_spacing": {"unit": "PERCENT", "size": 0, "sizes": []}, "counter_title_color": "#4c4f59", "margin": {"unit": "px", "top": "0", "right": "0", "bottom": "16", "left": "0", "isLinked": false}, "counter_icon_rotation": {"unit": "deg", "size": -240, "sizes": []}, "counter_icon_new": {"value": "fas fa-long-arrow-alt-right", "library": "fa-solid"}, "counter_icon_color": "", "counter_layout": "layout-6", "eael_icon_type": "icon", "counter_icon_size": {"unit": "px", "size": 48, "sizes": []}, "counter_icon_margin": {"unit": "px", "top": "0", "right": "43", "bottom": "0", "left": "0", "isLinked": false}, "counter_icon_padding": {"unit": "px", "top": "6", "right": "8", "bottom": "6", "left": "8", "isLinked": false}, "typography_typography": "custom", "background_background": "classic", "background_color": "#ffffff", "height": "min-height", "custom_height": {"unit": "px", "size": 152, "sizes": []}}, "elements": [], "isInner": false, "elType": "widget", "widgetType": "eael-counter"}], "isInner": false, "elType": "container"}, {"id": "429:1784", "settings": {"flex_direction": "row", "content_width": "boxed", "width": {"unit": "px", "size": 707, "sizes": []}, "padding": {"unit": "px", "top": "0", "right": "0", "bottom": "0", "left": "0", "isLinked": true}, "height": "min-height", "custom_height": {"unit": "px", "size": 160, "sizes": []}}, "elements": [{"id": "429:1785", "settings": {"i": {"unit": "px", "top": "16", "right": "16", "bottom": "16", "left": "16", "isLinked": true}, "_background_color": "#ffffff", "o": "classic", "ending_number": "25", "number_suffix": "%", "counter_num_typography_typography": "custom", "counter_num_typography_font_family": "Inter Tight", "counter_num_typography_font_weight": "600", "counter_num_typography_font_size": {"unit": "px", "size": 48, "sizes": []}, "counter_num_typography_line_height": {"unit": "%", "size": 120.00000476837158, "sizes": []}, "counter_num_typography_text_transform": "none", "counter_num_typography_letter_spacing": {"unit": "PERCENT", "size": 0, "sizes": []}, "section_number_suffix_typography_typography": "custom", "section_number_suffix_typography_font_family": "Inter Tight", "section_number_suffix_typography_font_weight": "600", "section_number_suffix_typography_font_size": {"unit": "px", "size": 48, "sizes": []}, "section_number_suffix_typography_line_height": {"unit": "%", "size": 120.00000476837158, "sizes": []}, "section_number_suffix_typography_text_transform": "none", "section_number_suffix_typography_letter_spacing": {"unit": "PERCENT", "size": 0, "sizes": []}, "counter_num_color": "#091439", "section_number_suffix_color": "#091439", "counter_title": "Company Growth", "counter_title_typography_typography": "custom", "counter_title_typography_font_family": "Inter Tight", "counter_title_typography_font_weight": "500", "counter_title_typography_font_size": {"unit": "px", "size": 18, "sizes": []}, "counter_title_typography_line_height": {"unit": "%", "size": 120.00000476837158, "sizes": []}, "counter_title_typography_text_transform": "none", "counter_title_typography_letter_spacing": {"unit": "PERCENT", "size": 0, "sizes": []}, "counter_title_color": "#4c4f59", "margin": {"unit": "px", "top": "0", "right": "0", "bottom": "16", "left": "0", "isLinked": false}, "counter_icon_rotation": {"unit": "deg", "size": -240, "sizes": []}, "counter_icon_new": {"value": "fas fa-long-arrow-alt-right", "library": "fa-solid"}, "counter_icon_color": "", "counter_layout": "layout-6", "eael_icon_type": "icon", "counter_icon_size": {"unit": "px", "size": 48, "sizes": []}, "counter_icon_margin": {"unit": "px", "top": "0", "right": "9", "bottom": "0", "left": "0", "isLinked": false}, "counter_icon_padding": {"unit": "px", "top": "8", "right": "6", "bottom": "8", "left": "6", "isLinked": false}, "typography_typography": "custom", "background_background": "classic", "background_color": "#ffffff", "height": "min-height", "custom_height": {"unit": "px", "size": 144, "sizes": []}}, "elements": [], "isInner": false, "elType": "widget", "widgetType": "eael-counter"}, {"id": "429:1794", "settings": {"i": {"unit": "px", "top": "16", "right": "16", "bottom": "16", "left": "16", "isLinked": true}, "_background_color": "#ffffff", "o": "classic", "ending_number": "100", "number_suffix": "+", "counter_num_typography_typography": "custom", "counter_num_typography_font_family": "Inter Tight", "counter_num_typography_font_weight": "600", "counter_num_typography_font_size": {"unit": "px", "size": 48, "sizes": []}, "counter_num_typography_line_height": {"unit": "%", "size": 120.00000476837158, "sizes": []}, "counter_num_typography_text_transform": "none", "counter_num_typography_letter_spacing": {"unit": "PERCENT", "size": 0, "sizes": []}, "section_number_suffix_typography_typography": "custom", "section_number_suffix_typography_font_family": "Inter Tight", "section_number_suffix_typography_font_weight": "600", "section_number_suffix_typography_font_size": {"unit": "px", "size": 48, "sizes": []}, "section_number_suffix_typography_line_height": {"unit": "%", "size": 120.00000476837158, "sizes": []}, "section_number_suffix_typography_text_transform": "none", "section_number_suffix_typography_letter_spacing": {"unit": "PERCENT", "size": 0, "sizes": []}, "counter_num_color": "#091439", "section_number_suffix_color": "#091439", "counter_title": "Team Member", "counter_title_typography_typography": "custom", "counter_title_typography_font_family": "Inter Tight", "counter_title_typography_font_weight": "500", "counter_title_typography_font_size": {"unit": "px", "size": 18, "sizes": []}, "counter_title_typography_line_height": {"unit": "%", "size": 120.00000476837158, "sizes": []}, "counter_title_typography_text_transform": "none", "counter_title_typography_letter_spacing": {"unit": "PERCENT", "size": 0, "sizes": []}, "counter_title_color": "#4c4f59", "margin": {"unit": "px", "top": "0", "right": "0", "bottom": "16", "left": "0", "isLinked": false}, "counter_icon_rotation": {"unit": "deg", "size": -240, "sizes": []}, "counter_icon_new": {"value": "fas fa-long-arrow-alt-right", "library": "fa-solid"}, "counter_icon_color": "", "counter_layout": "layout-6", "eael_icon_type": "icon", "counter_icon_size": {"unit": "px", "size": 48, "sizes": []}, "counter_icon_margin": {"unit": "px", "top": "0", "right": "149", "bottom": "0", "left": "0", "isLinked": false}, "counter_icon_padding": {"unit": "px", "top": "6", "right": "6", "bottom": "6", "left": "6", "isLinked": true}, "typography_typography": "custom", "background_background": "classic", "background_color": "#ffffff"}, "elements": [], "isInner": false, "elType": "widget", "widgetType": "eael-counter"}], "isInner": false, "elType": "container"}], "isInner": false, "elType": "container"}], "isInner": false, "elType": "container"}, {"id": "429:1804", "settings": {"flex_direction": "column", "content_width": "boxed", "width": {"unit": "px", "size": 532, "sizes": []}, "padding": {"unit": "px", "top": "147", "right": "66", "bottom": "147", "left": "66", "isLinked": false}, "background_background": "classic", "background_color": "#ffffff", "border_radius": {"unit": "px", "top": "16", "right": "16", "bottom": "16", "left": "16", "isLinked": true}}, "elements": [{"id": "429:1807", "settings": {"title": "We help to accelerate your business growth", "header_size": "h2", "t": "center", "typography_typography": "custom", "typography_font_size": {"unit": "px", "size": 56, "sizes": []}, "typography_line_height": {"unit": "%", "size": 120.00000476837158, "sizes": []}, "height": "min-height", "custom_height": {"unit": "px", "size": 268, "sizes": []}}, "elements": [], "isInner": false, "elType": "widget", "widgetType": "heading"}], "isInner": false, "elType": "container"}], "isInner": false, "elType": "container"}], "isInner": false, "elType": "container"}, {"id": "429:1808", "settings": {"flex_direction": "row", "content_width": "boxed", "width": {"unit": "px", "size": 1336, "sizes": []}, "flex_justify_content": "center", "padding": {"unit": "px", "top": "40", "right": "44", "bottom": "40", "left": "48", "isLinked": false}, "background_background": "classic", "background_color": "#091439", "border_radius": {"unit": "px", "top": "16", "right": "16", "bottom": "16", "left": "16", "isLinked": true}, "height": "min-height", "custom_height": {"unit": "px", "size": 568, "sizes": []}}, "elements": [{"id": "429:1809", "settings": {"flex_direction": "column", "content_width": "boxed", "width": {"unit": "px", "size": 342, "sizes": []}, "padding": {"unit": "px", "top": "0", "right": "0", "bottom": "0", "left": "0", "isLinked": true}, "background_background": "classic", "background_color": "#091439", "height": "min-height", "custom_height": {"unit": "px", "size": 420, "sizes": []}}, "elements": [{"id": "429:1864", "settings": {"flex_direction": "column", "content_width": "boxed", "width": {"unit": "px", "size": 342, "sizes": []}, "padding": {"unit": "px", "top": "0", "right": "0", "bottom": "0", "left": "0", "isLinked": true}, "height": "min-height", "custom_height": {"unit": "px", "size": 313, "sizes": []}}, "elements": [{"id": "429:1865", "settings": {"title": "AI engineering for  intelligent solutions", "header_size": "h2", "t": "flex-start", "typography_typography": "custom", "typography_font_family": "Inter Tight", "typography_font_weight": "600", "typography_font_size": {"unit": "px", "size": 48, "sizes": []}, "typography_line_height": {"unit": "%", "size": 129.99999523162842, "sizes": []}, "height": "min-height", "custom_height": {"unit": "px", "size": 186, "sizes": []}}, "elements": [], "isInner": false, "elType": "widget", "widgetType": "heading"}, {"id": "429:1866", "settings": {"editor": "<p>Utilizing AI and cutting-edge technologies to transform ideas into actionable strategies that deliver measurable results</p>", "typography_typography": "custom", "typography_font_family": "Inter Tight", "typography_font_weight": "400", "typography_font_size": {"unit": "px", "size": 18, "sizes": []}, "typography_line_height": {"unit": "%", "size": 160.0000023841858, "sizes": []}, "title_color": "#c4c8d7", "height": "min-height", "custom_height": {"unit": "px", "size": 87, "sizes": []}}, "elements": [], "isInner": false, "elType": "widget", "widgetType": "text-editor"}], "isInner": false, "elType": "container"}, {"id": "429:1867", "settings": {"ending_number": "2200", "number_suffix": "+", "counter_num_typography_typography": "custom", "counter_num_typography_font_family": "Inter Tight", "counter_num_typography_font_weight": "600", "counter_num_typography_font_size": {"unit": "px", "size": 48, "sizes": []}, "counter_num_typography_line_height": {"unit": "%", "size": 120.00000476837158, "sizes": []}, "counter_num_typography_text_transform": "none", "counter_num_typography_letter_spacing": {"unit": "PERCENT", "size": 0, "sizes": []}, "section_number_suffix_typography_typography": "custom", "section_number_suffix_typography_font_family": "Inter Tight", "section_number_suffix_typography_font_weight": "600", "section_number_suffix_typography_font_size": {"unit": "px", "size": 48, "sizes": []}, "section_number_suffix_typography_line_height": {"unit": "%", "size": 120.00000476837158, "sizes": []}, "section_number_suffix_typography_text_transform": "none", "section_number_suffix_typography_letter_spacing": {"unit": "PERCENT", "size": 0, "sizes": []}, "counter_num_color": "#ffffff", "section_number_suffix_color": "#ffffff", "counter_title": "Successful Projects", "counter_title_typography_typography": "custom", "counter_title_typography_font_family": "Inter Tight", "counter_title_typography_font_weight": "500", "counter_title_typography_font_size": {"unit": "px", "size": 24, "sizes": []}, "counter_title_typography_line_height": {"unit": "%", "size": 120.00000476837158, "sizes": []}, "counter_title_typography_text_transform": "none", "counter_title_typography_letter_spacing": {"unit": "PERCENT", "size": 0, "sizes": []}, "counter_title_color": "#c4c8d7", "margin": {"unit": "px", "top": "0", "right": "0", "bottom": "0", "left": "0", "isLinked": false}, "typography_typography": "custom", "height": "min-height", "custom_height": {"unit": "px", "size": 87, "sizes": []}}, "elements": [], "isInner": false, "elType": "widget", "widgetType": "eael-counter"}], "isInner": false, "elType": "container"}, {"id": "429:1870", "settings": {"flex_direction": "column", "content_width": "boxed", "width": {"unit": "px", "size": 340, "sizes": []}, "padding": {"unit": "px", "top": "0", "right": "0", "bottom": "0", "left": "0", "isLinked": true}, "background_background": "classic", "background_color": "#091439", "height": "min-height", "custom_height": {"unit": "px", "size": 488, "sizes": []}}, "elements": [{"id": "429:1871", "settings": {"flex_direction": "column", "content_width": "boxed", "width": {"unit": "px", "size": 346, "sizes": []}, "padding": {"unit": "px", "top": "0", "right": "0", "bottom": "0", "left": "0", "isLinked": true}, "height": "min-height", "custom_height": {"unit": "px", "size": 331, "sizes": []}}, "elements": [{"id": "429:1872", "settings": {"eael_feature_list_connector": "", "eael_feature_list": [{"eael_feature_list_title": "Predictive Analytics For Business Growth", "eael_feature_list_content": "", "eael_feature_list_icon_new": {"value": "fas fa-long-arrow-alt-right", "library": "fa-solid"}}, {"eael_feature_list_title": "Intelligent Chatbot Development", "eael_feature_list_content": "", "eael_feature_list_icon_new": {"value": "fas fa-long-arrow-alt-right", "library": "fa-solid"}}, {"eael_feature_list_title": "Personalized Marketing Automation", "eael_feature_list_content": "", "eael_feature_list_icon_new": {"value": "fas fa-long-arrow-alt-right", "library": "fa-solid"}}, {"eael_feature_list_title": "Advanced Image And Video Analysis", "eael_feature_list_content": "", "eael_feature_list_icon_new": {"value": "fas fa-long-arrow-alt-right", "library": "fa-solid"}}], "eael_infobox_button_icon_rotate": {"unit": "deg", "size": -240, "sizes": []}, "eael_feature_list_title_typography_typography": "custom", "eael_feature_list_title_typography_font_family": "Inter Tight", "eael_feature_list_title_typography_font_weight": "400", "eael_feature_list_title_typography_font_size": {"unit": "px", "size": 18, "sizes": []}, "eael_feature_list_title_typography_line_height": {"unit": "%", "size": 160.0000023841858, "sizes": []}, "eael_feature_list_title_typography_text_transform": "none", "eael_feature_list_title_typography_letter_spacing": {"unit": "PERCENT", "size": 0, "sizes": []}, "eael_feature_list_title_color": "#e3e6f1", "margin": {"unit": "px", "top": "0", "right": "8", "bottom": "0", "left": "0", "isLinked": false}, "eael_feature_list_icon_color": "", "eael_feature_list_icon_size": {"unit": "px", "size": 24, "sizes": []}, "eael_feature_list_icon_circle_size": {"unit": "px", "size": 24, "sizes": []}, "eael_feature_list_icon_space": {"unit": "px", "size": "8", "sizes": []}, "eael_feature_list_icon_padding": {"unit": "px", "top": "6", "right": "3", "bottom": "6", "left": "3", "isLinked": false}, "eael_feature_list_icon_background_color": "#091439", "eael_feature_list_icon_background_background": "classic", "typography_typography": "custom", "height": "min-height", "custom_height": {"unit": "px", "size": 188, "sizes": []}}, "elements": [], "isInner": false, "elType": "widget", "widgetType": "eael-feature-list"}, {"id": "429:1889", "settings": {"editor": "<p>Analyze data patterns, forecast trends, and make data-driven decisions that optimize your business operations</p>", "typography_typography": "custom", "typography_font_family": "Inter Tight", "typography_font_weight": "400", "typography_font_size": {"unit": "px", "size": 18, "sizes": []}, "typography_line_height": {"unit": "%", "size": 160.0000023841858, "sizes": []}, "title_color": "#c4c8d7", "height": "min-height", "custom_height": {"unit": "px", "size": 87, "sizes": []}}, "elements": [], "isInner": false, "elType": "widget", "widgetType": "text-editor"}], "isInner": false, "elType": "container"}, {"id": "429:1890", "settings": {"flex_direction": "row", "content_width": "boxed", "width": {"unit": "px", "size": 340, "sizes": []}, "padding": {"unit": "px", "top": "0", "right": "0", "bottom": "0", "left": "0", "isLinked": true}, "height": "min-height", "custom_height": {"unit": "px", "size": 22, "sizes": []}}, "elements": [{"id": "429:1891", "settings": {"title": "Let’s Get Started", "header_size": "h2", "t": "center", "typography_typography": "custom", "typography_font_family": "Inter Tight", "typography_font_weight": "500", "typography_font_size": {"unit": "px", "size": 18, "sizes": []}, "typography_line_height": {"unit": "%", "size": 120.00000476837158, "sizes": []}, "title_color": "#c4c8d7"}, "elements": [], "isInner": false, "elType": "widget", "widgetType": "heading"}], "isInner": false, "elType": "container"}], "isInner": false, "elType": "container"}], "isInner": false, "elType": "container"}, {"id": "429:1945", "settings": {"flex_direction": "column", "content_width": "boxed", "width": {"unit": "px", "size": 1318, "sizes": []}, "padding": {"unit": "px", "top": "0", "right": "0", "bottom": "0", "left": "0", "isLinked": true}, "height": "min-height", "custom_height": {"unit": "px", "size": 1174, "sizes": []}}, "elements": [{"id": "429:1946", "settings": {"flex_direction": "row", "content_width": "boxed", "width": {"unit": "px", "size": 1318, "sizes": []}, "flex_justify_content": "space-between", "padding": {"unit": "px", "top": "0", "right": "0", "bottom": "0", "left": "0", "isLinked": true}, "height": "min-height", "custom_height": {"unit": "px", "size": 73, "sizes": []}}, "elements": [{"id": "429:1947", "settings": {"eael_dch_first_title": "Our Popular Services", "eael_show_dch_icon_content": "", "eael_dch_last_title": "", "eael_dch_subtext": "", "eael_dch_first_title_typography_typography": "custom", "eael_dch_first_title_typography_font_family": "Inter Tight", "eael_dch_first_title_typography_font_weight": "600", "eael_dch_first_title_typography_font_size": {"unit": "px", "size": 56, "sizes": []}, "eael_dch_first_title_typography_line_height": {"unit": "%", "size": 129.99999523162842, "sizes": []}, "eael_dch_first_title_typography_text_transform": "none", "eael_dch_first_title_typography_letter_spacing": {"unit": "PERCENT", "size": 0, "sizes": []}, "eael_dch_base_title_color": "#091439", "eael_dch_dual_title_color": "#091439", "eael_dch_container_margin": {"unit": "px", "top": "0", "right": "0", "bottom": "0", "left": "0", "isLinked": false}, "typography_typography": "custom", "typography_font_family": "Inter Tight", "typography_font_weight": "600", "typography_font_size": {"unit": "px", "size": 56, "sizes": []}, "typography_line_height": {"unit": "%", "size": 129.99999523162842, "sizes": []}, "title_color": "#091439"}, "elements": [], "isInner": false, "elType": "widget", "widgetType": "eael-dual-color-header"}, {"id": "429:1948", "settings": {"creative_button_text": "View All Servies", "eael_creative_button_typography_typography": "custom", "eael_creative_button_typography_font_family": "Inter Tight", "eael_creative_button_typography_font_weight": "500", "eael_creative_button_typography_font_size": {"unit": "px", "size": 18, "sizes": []}, "eael_creative_button_typography_line_height": {"unit": "%", "size": 110.00000238418579, "sizes": []}, "eael_creative_button_typography_text_transform": "none", "eael_creative_button_typography_letter_spacing": {"unit": "PERCENT", "size": 0, "sizes": []}, "eael_creative_button_text_color": "#091439", "eael_creative_button_hover_text_color": "#091439", "eael_creative_button_icon_color": "#091439", "eael_creative_button_hover_icon_color": "#091439", "margin": {"unit": "px", "top": "0", "right": "8", "bottom": "0", "left": "0", "isLinked": false}, "eael_creative_button_icon_new": {"value": "fas fa-long-arrow-alt-right", "library": "fa-solid"}, "eael_creative_button_icon_alignment": "right", "eael_creative_button_icon_indent": {"unit": "px", "size": 8, "sizes": []}, "eael_creative_button_icon_size": {"unit": "px", "size": 24, "sizes": []}, "eael_creative_button_background_color": "#ffffff", "eael_creative_button_hover_background_color": "#ffffff", "typography_typography": "custom", "background_background": "classic", "background_color": "#ffffff", "height": "min-height", "custom_height": {"unit": "px", "size": 24, "sizes": []}}, "elements": [], "isInner": false, "elType": "widget", "widgetType": "eael-creative-button"}], "isInner": false, "elType": "container"}, {"id": "429:1953", "settings": {"flex_direction": "column", "content_width": "boxed", "width": {"unit": "px", "size": 1318, "sizes": []}, "padding": {"unit": "px", "top": "0", "right": "0", "bottom": "0", "left": "0", "isLinked": true}, "height": "min-height", "custom_height": {"unit": "px", "size": 1045, "sizes": []}}, "elements": [{"id": "429:1954", "settings": {"flex_direction": "row", "content_width": "boxed", "width": {"unit": "px", "size": 1318, "sizes": []}, "padding": {"unit": "px", "top": "0", "right": "0", "bottom": "0", "left": "0", "isLinked": true}, "height": "min-height", "custom_height": {"unit": "px", "size": 443, "sizes": []}}, "elements": [{"id": "429:1955", "settings": {"eael_show_infobox_clickable": "", "eael_infobox_button_margin": {"unit": "px", "top": "0", "right": "0", "bottom": "40", "left": "0", "isLinked": false}, "eael_infobox_img_or_icon": "img", "eael_infobox_content_alignment": "left", "eael_infobox_img_type": "img-on-right", "icon_vertical_position": "top", "eael_infobox_image_resizer": {"unit": "px", "size": 271, "sizes": []}, "eael_infobox_image": {"url": "https://api.figma.com/v1/images/70a9765a4d1dcaefe435173e6a374aad06b903e2", "id": "", "size": "", "alt": "", "source": "figma"}, "eael_section_infobox_container_bg": "#f5f1ff", "eael_section_infobox_container_padding": {"unit": "px", "top": "40", "right": "0", "bottom": "40", "left": "40", "isLinked": false}, "eael_infobox_title": "Brand Strategy Development", "eael_infobox_title_typography_typography": "custom", "eael_infobox_title_typography_font_family": "Inter Tight", "eael_infobox_title_typography_font_weight": "600", "eael_infobox_title_typography_font_size": {"unit": "px", "size": 32, "sizes": []}, "eael_infobox_title_typography_line_height": {"unit": "%", "size": 129.99999523162842, "sizes": []}, "eael_infobox_title_typography_text_transform": "none", "eael_infobox_title_typography_letter_spacing": {"unit": "PERCENT", "size": 0, "sizes": []}, "eael_infobox_title_color": "#091439", "eael_infobox_title_margin": {"unit": "px", "top": "0", "right": "0", "bottom": "16", "left": "0", "isLinked": false}, "eael_infobox_content_typography_hover_typography": "custom", "eael_infobox_content_typography_hover_font_family": "Inter Tight", "eael_infobox_content_typography_hover_font_weight": "400", "eael_infobox_content_typography_hover_font_size": {"unit": "px", "size": 18, "sizes": []}, "eael_infobox_content_typography_hover_line_height": {"unit": "%", "size": 160.0000023841858, "sizes": []}, "eael_infobox_content_typography_hover_text_transform": "none", "eael_infobox_content_typography_hover_letter_spacing": {"unit": "PERCENT", "size": 0, "sizes": []}, "eael_infobox_content_color": "#727686", "eael_infobox_content_margin": {"unit": "px", "top": "0", "right": "0", "bottom": "16", "left": "0", "isLinked": false}, "eael_infobox_text": "<p>Build comprehensive strategies to position your brand & create a lasting impact in the digital world</p>", "infobox_button_text": "Learn More", "eael_show_infobox_button": "yes", "eael_infobox_button_typography_typography": "custom", "eael_infobox_button_typography_font_family": "Inter Tight", "eael_infobox_button_typography_font_weight": "500", "eael_infobox_button_typography_font_size": {"unit": "px", "size": 16, "sizes": []}, "eael_infobox_button_typography_line_height": {"unit": "%", "size": 110.00000238418579, "sizes": []}, "eael_infobox_button_typography_text_transform": "none", "eael_infobox_button_typography_letter_spacing": {"unit": "PERCENT", "size": 0, "sizes": []}, "eael_infobox_button_text_color": "#091439", "eael_infobox_button_hover_text_color": "#091439", "eael_infobox_button_background_color": "#f5f1ff", "eael_infobox_button_background_color_color": "#f5f1ff", "eael_infobox_button_hover_background_color_color": "#f5f1ff", "eael_infobox_button_hover_background_color": "#f5f1ff", "eael_creative_button_padding": {"unit": "px", "top": "0", "right": "0", "bottom": "0", "left": "0", "isLinked": true}, "eael_infobox_button_border_width": {"unit": "px", "top": "1", "right": "1", "bottom": "1", "left": "1", "isLinked": true}, "eael_infobox_button_border_border": "solid", "eael_infobox_button_border_color": "#f5f1ff", "eael_infobox_button_icon_rotate": {"unit": "deg", "size": 320, "sizes": []}, "eael_infobox_button_icon_new": {"value": "fas fa-long-arrow-alt-right", "library": "fa-solid"}, "eael_infobox_button_icon_size": {"unit": "px", "size": 24, "sizes": []}, "eael_infobox_button_icon_indent": {"unit": "px", "size": 4, "sizes": []}, "eael_infobox_button_icon_alignment": "right", "typography_typography": "custom", "background_background": "classic", "background_color": "#f5f1ff"}, "elements": [], "isInner": false, "elType": "widget", "widgetType": "eael-info-box"}, {"id": "429:1966", "settings": {"eael_show_infobox_clickable": "", "eael_infobox_button_margin": {"unit": "px", "top": "0", "right": "0", "bottom": "40", "left": "0", "isLinked": false}, "eael_infobox_img_or_icon": "img", "eael_infobox_content_alignment": "left", "eael_infobox_img_type": "img-on-right", "icon_vertical_position": "middle", "eael_infobox_image_resizer": {"unit": "px", "size": 258.90966796875, "sizes": []}, "eael_infobox_image": {"url": "https://api.figma.com/v1/images/166b520ff8e2275e5e9b6c4846489609bdd8f10e", "id": "", "size": "", "alt": "", "source": "figma"}, "eael_section_infobox_container_bg": "#feebff", "eael_section_infobox_container_padding": {"unit": "px", "top": "0", "right": "0", "bottom": "0", "left": "40", "isLinked": false}, "eael_infobox_title": "SEO Analytics & Reporting", "eael_infobox_title_typography_typography": "custom", "eael_infobox_title_typography_font_family": "Inter Tight", "eael_infobox_title_typography_font_weight": "600", "eael_infobox_title_typography_font_size": {"unit": "px", "size": 32, "sizes": []}, "eael_infobox_title_typography_line_height": {"unit": "%", "size": 129.99999523162842, "sizes": []}, "eael_infobox_title_typography_text_transform": "none", "eael_infobox_title_typography_letter_spacing": {"unit": "PERCENT", "size": 0, "sizes": []}, "eael_infobox_title_color": "#091439", "eael_infobox_title_margin": {"unit": "px", "top": "0", "right": "0", "bottom": "16", "left": "0", "isLinked": false}, "eael_infobox_content_typography_hover_typography": "custom", "eael_infobox_content_typography_hover_font_family": "Inter Tight", "eael_infobox_content_typography_hover_font_weight": "400", "eael_infobox_content_typography_hover_font_size": {"unit": "px", "size": 18, "sizes": []}, "eael_infobox_content_typography_hover_line_height": {"unit": "%", "size": 160.0000023841858, "sizes": []}, "eael_infobox_content_typography_hover_text_transform": "none", "eael_infobox_content_typography_hover_letter_spacing": {"unit": "PERCENT", "size": 0, "sizes": []}, "eael_infobox_content_color": "#727686", "eael_infobox_content_margin": {"unit": "px", "top": "0", "right": "0", "bottom": "16", "left": "0", "isLinked": false}, "eael_infobox_text": "<p>Data-driven SEO techniques to improve organic traffic and boost search rankings.</p>", "infobox_button_text": "Learn More", "eael_show_infobox_button": "yes", "eael_infobox_button_typography_typography": "custom", "eael_infobox_button_typography_font_family": "Inter Tight", "eael_infobox_button_typography_font_weight": "500", "eael_infobox_button_typography_font_size": {"unit": "px", "size": 16, "sizes": []}, "eael_infobox_button_typography_line_height": {"unit": "%", "size": 110.00000238418579, "sizes": []}, "eael_infobox_button_typography_text_transform": "none", "eael_infobox_button_typography_letter_spacing": {"unit": "PERCENT", "size": 0, "sizes": []}, "eael_infobox_button_text_color": "#091439", "eael_infobox_button_hover_text_color": "#091439", "eael_infobox_button_background_color": "#feebff", "eael_infobox_button_background_color_color": "#feebff", "eael_infobox_button_hover_background_color_color": "#feebff", "eael_infobox_button_hover_background_color": "#feebff", "eael_creative_button_padding": {"unit": "px", "top": "0", "right": "0", "bottom": "0", "left": "0", "isLinked": true}, "eael_infobox_button_border_width": {"unit": "px", "top": "1", "right": "1", "bottom": "1", "left": "1", "isLinked": true}, "eael_infobox_button_border_border": "solid", "eael_infobox_button_border_color": "#feebff", "eael_infobox_button_icon_rotate": {"unit": "deg", "size": 320, "sizes": []}, "eael_infobox_button_icon_new": {"value": "fas fa-long-arrow-alt-right", "library": "fa-solid"}, "eael_infobox_button_icon_size": {"unit": "px", "size": 24, "sizes": []}, "eael_infobox_button_icon_indent": {"unit": "px", "size": 4, "sizes": []}, "eael_infobox_button_icon_alignment": "right", "typography_typography": "custom", "background_background": "classic", "background_color": "#feebff", "height": "min-height", "custom_height": {"unit": "px", "size": 441, "sizes": []}}, "elements": [], "isInner": false, "elType": "widget", "widgetType": "eael-info-box"}], "isInner": false, "elType": "container"}, {"id": "429:1977", "settings": {"flex_direction": "row", "content_width": "boxed", "width": {"unit": "px", "size": 1318, "sizes": []}, "padding": {"unit": "px", "top": "0", "right": "0", "bottom": "0", "left": "0", "isLinked": true}, "height": "min-height", "custom_height": {"unit": "px", "size": 573, "sizes": []}}, "elements": [{"id": "429:1978", "settings": {"eael_show_infobox_clickable": "", "eael_infobox_button_margin": {"unit": "px", "top": "0", "right": "0", "bottom": "40", "left": "0", "isLinked": false}, "eael_infobox_img_or_icon": "img", "eael_infobox_content_alignment": "left", "eael_infobox_img_type": "img-on-bottom", "icon_vertical_position": "top", "eael_infobox_image_resizer": {"unit": "px", "size": 384, "sizes": []}, "eael_infobox_image": {"url": "https://api.figma.com/v1/images/bea26f4b520d27af2347e1798e344c9c6e6fa20c", "id": "", "size": "", "alt": "", "source": "figma"}, "eael_infobox_img_margin": {"unit": "px", "top": "0", "right": "0", "bottom": "24", "left": "0", "isLinked": false}, "eael_section_infobox_container_bg": "#fff6eb", "eael_section_infobox_container_padding": {"unit": "px", "top": "40", "right": "0", "bottom": "0", "left": "40", "isLinked": false}, "eael_infobox_title": "Creative Content Production", "eael_infobox_title_typography_typography": "custom", "eael_infobox_title_typography_font_family": "Inter Tight", "eael_infobox_title_typography_font_weight": "600", "eael_infobox_title_typography_font_size": {"unit": "px", "size": 32, "sizes": []}, "eael_infobox_title_typography_line_height": {"unit": "%", "size": 129.99999523162842, "sizes": []}, "eael_infobox_title_typography_text_transform": "none", "eael_infobox_title_typography_letter_spacing": {"unit": "PERCENT", "size": 0, "sizes": []}, "eael_infobox_title_color": "#091439", "eael_infobox_title_margin": {"unit": "px", "top": "0", "right": "0", "bottom": "16", "left": "0", "isLinked": false}, "eael_infobox_content_typography_hover_typography": "custom", "eael_infobox_content_typography_hover_font_family": "Inter Tight", "eael_infobox_content_typography_hover_font_weight": "400", "eael_infobox_content_typography_hover_font_size": {"unit": "px", "size": 18, "sizes": []}, "eael_infobox_content_typography_hover_line_height": {"unit": "%", "size": 160.0000023841858, "sizes": []}, "eael_infobox_content_typography_hover_text_transform": "none", "eael_infobox_content_typography_hover_letter_spacing": {"unit": "PERCENT", "size": 0, "sizes": []}, "eael_infobox_content_color": "#727686", "eael_infobox_content_margin": {"unit": "px", "top": "0", "right": "0", "bottom": "16", "left": "0", "isLinked": false}, "eael_infobox_text": "<p>Delivering content that resonates, inspires, and drives customer engagement</p>", "infobox_button_text": "Learn More", "eael_show_infobox_button": "yes", "eael_infobox_button_typography_typography": "custom", "eael_infobox_button_typography_font_family": "Inter Tight", "eael_infobox_button_typography_font_weight": "500", "eael_infobox_button_typography_font_size": {"unit": "px", "size": 16, "sizes": []}, "eael_infobox_button_typography_line_height": {"unit": "%", "size": 110.00000238418579, "sizes": []}, "eael_infobox_button_typography_text_transform": "none", "eael_infobox_button_typography_letter_spacing": {"unit": "PERCENT", "size": 0, "sizes": []}, "eael_infobox_button_text_color": "#091439", "eael_infobox_button_hover_text_color": "#091439", "eael_infobox_button_background_color": "#fff6eb", "eael_infobox_button_background_color_color": "#fff6eb", "eael_infobox_button_hover_background_color_color": "#fff6eb", "eael_infobox_button_hover_background_color": "#fff6eb", "eael_creative_button_padding": {"unit": "px", "top": "0", "right": "0", "bottom": "0", "left": "0", "isLinked": true}, "eael_infobox_button_border_width": {"unit": "px", "top": "1", "right": "1", "bottom": "1", "left": "1", "isLinked": true}, "eael_infobox_button_border_border": "solid", "eael_infobox_button_border_color": "#fff6eb", "eael_infobox_button_icon_rotate": {"unit": "deg", "size": 320, "sizes": []}, "eael_infobox_button_icon_new": {"value": "fas fa-long-arrow-alt-right", "library": "fa-solid"}, "eael_infobox_button_icon_size": {"unit": "px", "size": 24, "sizes": []}, "eael_infobox_button_icon_indent": {"unit": "px", "size": 4, "sizes": []}, "eael_infobox_button_icon_alignment": "right", "typography_typography": "custom", "background_background": "classic", "background_color": "#fff6eb"}, "elements": [], "isInner": false, "elType": "widget", "widgetType": "eael-info-box"}, {"id": "429:1989", "settings": {"eael_show_infobox_clickable": "", "eael_infobox_button_margin": {"unit": "px", "top": "0", "right": "0", "bottom": "40", "left": "0", "isLinked": false}, "eael_infobox_img_or_icon": "img", "eael_infobox_content_alignment": "left", "eael_infobox_img_type": "img-on-bottom", "icon_vertical_position": "top", "eael_infobox_image_resizer": {"unit": "px", "size": 341, "sizes": []}, "eael_infobox_image": {"url": "https://api.figma.com/v1/images/40760fc3cd464c9b56531500c0481a16d07d6665", "id": "", "size": "", "alt": "", "source": "figma"}, "eael_infobox_img_margin": {"unit": "px", "top": "0", "right": "0", "bottom": "24", "left": "0", "isLinked": false}, "eael_section_infobox_container_bg": "#f2fbee", "eael_section_infobox_container_padding": {"unit": "px", "top": "40", "right": "0", "bottom": "0", "left": "40", "isLinked": false}, "eael_infobox_title": "App Design & Development", "eael_infobox_title_typography_typography": "custom", "eael_infobox_title_typography_font_family": "Inter Tight", "eael_infobox_title_typography_font_weight": "600", "eael_infobox_title_typography_font_size": {"unit": "px", "size": 32, "sizes": []}, "eael_infobox_title_typography_line_height": {"unit": "%", "size": 129.99999523162842, "sizes": []}, "eael_infobox_title_typography_text_transform": "none", "eael_infobox_title_typography_letter_spacing": {"unit": "PERCENT", "size": 0, "sizes": []}, "eael_infobox_title_color": "#091439", "eael_infobox_title_margin": {"unit": "px", "top": "0", "right": "0", "bottom": "16", "left": "0", "isLinked": false}, "eael_infobox_content_typography_hover_typography": "custom", "eael_infobox_content_typography_hover_font_family": "Inter Tight", "eael_infobox_content_typography_hover_font_weight": "400", "eael_infobox_content_typography_hover_font_size": {"unit": "px", "size": 18, "sizes": []}, "eael_infobox_content_typography_hover_line_height": {"unit": "%", "size": 160.0000023841858, "sizes": []}, "eael_infobox_content_typography_hover_text_transform": "none", "eael_infobox_content_typography_hover_letter_spacing": {"unit": "PERCENT", "size": 0, "sizes": []}, "eael_infobox_content_color": "#727686", "eael_infobox_content_margin": {"unit": "px", "top": "0", "right": "0", "bottom": "16", "left": "0", "isLinked": false}, "eael_infobox_text": "<p>Develop custom apps that align with your business goals and user expectations.</p>", "infobox_button_text": "Learn More", "eael_show_infobox_button": "yes", "eael_infobox_button_typography_typography": "custom", "eael_infobox_button_typography_font_family": "Inter Tight", "eael_infobox_button_typography_font_weight": "500", "eael_infobox_button_typography_font_size": {"unit": "px", "size": 16, "sizes": []}, "eael_infobox_button_typography_line_height": {"unit": "%", "size": 110.00000238418579, "sizes": []}, "eael_infobox_button_typography_text_transform": "none", "eael_infobox_button_typography_letter_spacing": {"unit": "PERCENT", "size": 0, "sizes": []}, "eael_infobox_button_text_color": "#091439", "eael_infobox_button_hover_text_color": "#091439", "eael_infobox_button_background_color": "#f2fbee", "eael_infobox_button_background_color_color": "#f2fbee", "eael_infobox_button_hover_background_color_color": "#f2fbee", "eael_infobox_button_hover_background_color": "#f2fbee", "eael_creative_button_padding": {"unit": "px", "top": "0", "right": "0", "bottom": "0", "left": "0", "isLinked": true}, "eael_infobox_button_border_width": {"unit": "px", "top": "1", "right": "1", "bottom": "1", "left": "1", "isLinked": true}, "eael_infobox_button_border_border": "solid", "eael_infobox_button_border_color": "#f2fbee", "eael_infobox_button_icon_rotate": {"unit": "deg", "size": 320, "sizes": []}, "eael_infobox_button_icon_new": {"value": "fas fa-long-arrow-alt-right", "library": "fa-solid"}, "eael_infobox_button_icon_size": {"unit": "px", "size": 24, "sizes": []}, "eael_infobox_button_icon_indent": {"unit": "px", "size": 4, "sizes": []}, "eael_infobox_button_icon_alignment": "right", "typography_typography": "custom", "background_background": "classic", "background_color": "#f2fbee"}, "elements": [], "isInner": false, "elType": "widget", "widgetType": "eael-info-box"}, {"id": "429:2000", "settings": {"eael_show_infobox_clickable": "", "eael_infobox_button_margin": {"unit": "px", "top": "0", "right": "0", "bottom": "40", "left": "0", "isLinked": false}, "eael_infobox_img_or_icon": "img", "eael_infobox_content_alignment": "left", "eael_infobox_img_type": "img-on-bottom", "icon_vertical_position": "top", "eael_infobox_image_resizer": {"unit": "px", "size": 244.109130859375, "sizes": []}, "eael_infobox_image": {"url": "https://api.figma.com/v1/images/3bac193a587397e2ee7046f6b5d4fc1dfc3dc4ea", "id": "", "size": "", "alt": "", "source": "figma"}, "eael_infobox_img_margin": {"unit": "px", "top": "0", "right": "0", "bottom": "24", "left": "0", "isLinked": false}, "eael_section_infobox_container_bg": "#edf7fa", "eael_section_infobox_container_padding": {"unit": "px", "top": "40", "right": "0", "bottom": "0", "left": "40", "isLinked": false}, "eael_infobox_title": "Consultation Strategy Service", "eael_infobox_title_typography_typography": "custom", "eael_infobox_title_typography_font_family": "Inter Tight", "eael_infobox_title_typography_font_weight": "600", "eael_infobox_title_typography_font_size": {"unit": "px", "size": 32, "sizes": []}, "eael_infobox_title_typography_line_height": {"unit": "%", "size": 129.99999523162842, "sizes": []}, "eael_infobox_title_typography_text_transform": "none", "eael_infobox_title_typography_letter_spacing": {"unit": "PERCENT", "size": 0, "sizes": []}, "eael_infobox_title_color": "#091439", "eael_infobox_title_margin": {"unit": "px", "top": "0", "right": "0", "bottom": "16", "left": "0", "isLinked": false}, "eael_infobox_content_typography_hover_typography": "custom", "eael_infobox_content_typography_hover_font_family": "Inter Tight", "eael_infobox_content_typography_hover_font_weight": "400", "eael_infobox_content_typography_hover_font_size": {"unit": "px", "size": 18, "sizes": []}, "eael_infobox_content_typography_hover_line_height": {"unit": "%", "size": 160.0000023841858, "sizes": []}, "eael_infobox_content_typography_hover_text_transform": "none", "eael_infobox_content_typography_hover_letter_spacing": {"unit": "PERCENT", "size": 0, "sizes": []}, "eael_infobox_content_color": "#727686", "eael_infobox_content_margin": {"unit": "px", "top": "0", "right": "0", "bottom": "16", "left": "0", "isLinked": false}, "eael_infobox_text": "<p>Personalized consulting to create actionable strategies for your digital success.</p>", "infobox_button_text": "Learn More", "eael_show_infobox_button": "yes", "eael_infobox_button_typography_typography": "custom", "eael_infobox_button_typography_font_family": "Inter Tight", "eael_infobox_button_typography_font_weight": "500", "eael_infobox_button_typography_font_size": {"unit": "px", "size": 16, "sizes": []}, "eael_infobox_button_typography_line_height": {"unit": "%", "size": 110.00000238418579, "sizes": []}, "eael_infobox_button_typography_text_transform": "none", "eael_infobox_button_typography_letter_spacing": {"unit": "PERCENT", "size": 0, "sizes": []}, "eael_infobox_button_text_color": "#091439", "eael_infobox_button_hover_text_color": "#091439", "eael_infobox_button_background_color": "#edf7fa", "eael_infobox_button_background_color_color": "#edf7fa", "eael_infobox_button_hover_background_color_color": "#edf7fa", "eael_infobox_button_hover_background_color": "#edf7fa", "eael_creative_button_padding": {"unit": "px", "top": "0", "right": "0", "bottom": "0", "left": "0", "isLinked": true}, "eael_infobox_button_border_width": {"unit": "px", "top": "1", "right": "1", "bottom": "1", "left": "1", "isLinked": true}, "eael_infobox_button_border_border": "solid", "eael_infobox_button_border_color": "#edf7fa", "eael_infobox_button_icon_rotate": {"unit": "deg", "size": 320, "sizes": []}, "eael_infobox_button_icon_new": {"value": "fas fa-long-arrow-alt-right", "library": "fa-solid"}, "eael_infobox_button_icon_size": {"unit": "px", "size": 24, "sizes": []}, "eael_infobox_button_icon_indent": {"unit": "px", "size": 4, "sizes": []}, "eael_infobox_button_icon_alignment": "right", "typography_typography": "custom", "background_background": "classic", "background_color": "#edf7fa"}, "elements": [], "isInner": false, "elType": "widget", "widgetType": "eael-info-box"}], "isInner": false, "elType": "container"}], "isInner": false, "elType": "container"}], "isInner": false, "elType": "container"}, {"id": "429:2011", "settings": {"flex_direction": "column", "content_width": "boxed", "width": {"unit": "px", "size": 1320, "sizes": []}, "padding": {"unit": "px", "top": "0", "right": "0", "bottom": "0", "left": "0", "isLinked": true}, "height": "min-height", "custom_height": {"unit": "px", "size": 1360, "sizes": []}}, "elements": [{"id": "429:2012", "settings": {"eael_dch_first_title": "Success Stories from Our Client", "eael_show_dch_icon_content": "", "eael_dch_last_title": "", "eael_dch_subtext": "", "eael_dch_first_title_typography_typography": "custom", "eael_dch_first_title_typography_font_family": "Inter Tight", "eael_dch_first_title_typography_font_weight": "600", "eael_dch_first_title_typography_font_size": {"unit": "px", "size": 48, "sizes": []}, "eael_dch_first_title_typography_line_height": {"unit": "%", "size": 129.99999523162842, "sizes": []}, "eael_dch_first_title_typography_text_transform": "none", "eael_dch_first_title_typography_letter_spacing": {"unit": "PERCENT", "size": 0, "sizes": []}, "eael_dch_base_title_color": "#091439", "eael_dch_dual_title_color": "#091439", "eael_dch_container_margin": {"unit": "px", "top": "0", "right": "0", "bottom": "0", "left": "0", "isLinked": false}, "typography_typography": "custom", "typography_font_family": "Inter Tight", "typography_font_weight": "600", "typography_font_size": {"unit": "px", "size": 48, "sizes": []}, "typography_line_height": {"unit": "%", "size": 129.99999523162842, "sizes": []}, "title_color": "#091439", "height": "min-height", "custom_height": {"unit": "px", "size": 62, "sizes": []}}, "elements": [], "isInner": false, "elType": "widget", "widgetType": "eael-dual-color-header"}, {"id": "429:2013", "settings": {"flex_direction": "column", "content_width": "boxed", "width": {"unit": "px", "size": 1320, "sizes": []}, "padding": {"unit": "px", "top": "0", "right": "0", "bottom": "0", "left": "0", "isLinked": true}, "height": "min-height", "custom_height": {"unit": "px", "size": 1242, "sizes": []}}, "elements": [{"id": "429:2014", "settings": {"flex_direction": "column", "content_width": "boxed", "width": {"unit": "px", "size": 1320, "sizes": []}, "padding": {"unit": "px", "top": "0", "right": "0", "bottom": "0", "left": "0", "isLinked": true}, "height": "min-height", "custom_height": {"unit": "px", "size": 1154, "sizes": []}}, "elements": [{"id": "429:2015", "settings": {"eael_show_infobox_clickable": "", "eael_infobox_button_margin": {"unit": "px", "top": "0", "right": "0", "bottom": "40", "left": "0", "isLinked": false}, "eael_infobox_img_or_icon": "img", "eael_infobox_content_alignment": "left", "eael_infobox_img_type": "img-on-right", "icon_vertical_position": "top", "eael_infobox_image_resizer": {"unit": "px", "size": 550, "sizes": []}, "eael_infobox_image": {"url": "https://api.figma.com/v1/images/b077a7ae5a391a7f76814452557ec9f9502dbb8d", "id": "", "size": "", "alt": "", "source": "figma"}, "eael_infobox_img_shape": "radius", "eael_infobox_img_shape_radius": {"unit": "px", "top": "8", "right": "8", "bottom": "8", "left": "8", "isLinked": true}, "eael_section_infobox_container_bg": "#f5f1ff", "eael_section_infobox_container_padding": {"unit": "px", "top": "40", "right": "40", "bottom": "40", "left": "40", "isLinked": true}, "eael_infobox_title": "Responsive Web Design For Mobile Excellence", "eael_infobox_title_typography_typography": "custom", "eael_infobox_title_typography_font_family": "Inter Tight", "eael_infobox_title_typography_font_weight": "600", "eael_infobox_title_typography_font_size": {"unit": "px", "size": 32, "sizes": []}, "eael_infobox_title_typography_line_height": {"unit": "%", "size": 129.99999523162842, "sizes": []}, "eael_infobox_title_typography_text_transform": "none", "eael_infobox_title_typography_letter_spacing": {"unit": "PERCENT", "size": 0, "sizes": []}, "eael_infobox_title_color": "#091439", "eael_infobox_title_margin": {"unit": "px", "top": "0", "right": "0", "bottom": "16", "left": "0", "isLinked": false}, "eael_infobox_sub_title": "Web Design & Development", "eael_infobox_sub_title_typography_typography": "custom", "eael_infobox_sub_title_typography_font_family": "Inter Tight", "eael_infobox_sub_title_typography_font_weight": "400", "eael_infobox_sub_title_typography_font_size": {"unit": "px", "size": 18, "sizes": []}, "eael_infobox_sub_title_typography_line_height": {"unit": "%", "size": 160.0000023841858, "sizes": []}, "eael_infobox_sub_title_typography_text_transform": "none", "eael_infobox_sub_title_typography_letter_spacing": {"unit": "PERCENT", "size": 0, "sizes": []}, "eael_infobox_sub_title_color": "#cb8ff3", "eael_infobox_subtitle_margin": {"unit": "px", "top": "0", "right": "0", "bottom": "16", "left": "0", "isLinked": false}, "eael_infobox_content_typography_hover_typography": "custom", "eael_infobox_content_typography_hover_font_family": "Inter Tight", "eael_infobox_content_typography_hover_font_weight": "400", "eael_infobox_content_typography_hover_font_size": {"unit": "px", "size": 18, "sizes": []}, "eael_infobox_content_typography_hover_line_height": {"unit": "%", "size": 160.0000023841858, "sizes": []}, "eael_infobox_content_typography_hover_text_transform": "none", "eael_infobox_content_typography_hover_letter_spacing": {"unit": "PERCENT", "size": 0, "sizes": []}, "eael_infobox_content_color": "#727686", "eael_infobox_content_margin": {"unit": "px", "top": "0", "right": "0", "bottom": "16", "left": "0", "isLinked": false}, "eael_infobox_text": "<p>They helped us provide a streamlined user experience with innovative technology, ensuring seamless navigation.</p>", "infobox_button_text": "View All Case", "eael_show_infobox_button": "yes", "eael_infobox_button_typography_typography": "custom", "eael_infobox_button_typography_font_family": "Inter Tight", "eael_infobox_button_typography_font_weight": "500", "eael_infobox_button_typography_font_size": {"unit": "px", "size": 16, "sizes": []}, "eael_infobox_button_typography_line_height": {"unit": "%", "size": 110.00000238418579, "sizes": []}, "eael_infobox_button_typography_text_transform": "none", "eael_infobox_button_typography_letter_spacing": {"unit": "PERCENT", "size": 0, "sizes": []}, "eael_infobox_button_text_color": "#ffffff", "eael_infobox_button_hover_text_color": "#ffffff", "eael_infobox_button_background_color": "#313c5e", "eael_infobox_button_background_color_color": "#313c5e", "eael_infobox_button_hover_background_color_color": "#313c5e", "eael_infobox_button_hover_background_color": "#313c5e", "eael_infobox_button_background_color_color_b": "#313c5e", "eael_infobox_button_hover_background_color_color_b": "#313c5e", "eael_infobox_button_background_color_b": "#313c5e", "eael_infobox_button_hover_background_color_b": "#313c5e", "eael_creative_button_padding": {"unit": "px", "top": "16", "right": "24", "bottom": "16", "left": "24", "isLinked": false}, "eael_infobox_button_border_radius": {"unit": "px", "size": "4", "sizes": []}, "eael_infobox_button_border_width": {"unit": "px", "top": "2", "right": "2", "bottom": "2", "left": "2", "isLinked": true}, "eael_infobox_button_border_border": "solid", "eael_infobox_button_border_color": "#091439", "eael_infobox_button_icon_rotate": {"unit": "deg", "size": -240, "sizes": []}, "eael_infobox_button_icon_new": {"value": "fas fa-long-arrow-alt-right", "library": "fa-solid"}, "eael_infobox_button_icon_size": {"unit": "px", "size": 24, "sizes": []}, "eael_infobox_button_icon_indent": {"unit": "px", "size": 10, "sizes": []}, "eael_infobox_button_icon_alignment": "right", "typography_typography": "custom", "background_background": "classic", "background_color": "#f5f1ff", "height": "min-height", "custom_height": {"unit": "px", "size": 490, "sizes": []}}, "elements": [], "isInner": false, "elType": "widget", "widgetType": "eael-info-box"}, {"id": "429:2028", "settings": {"flex_direction": "row", "content_width": "boxed", "width": {"unit": "px", "size": 1320, "sizes": []}, "padding": {"unit": "px", "top": "0", "right": "0", "bottom": "0", "left": "0", "isLinked": true}, "height": "min-height", "custom_height": {"unit": "px", "size": 640, "sizes": []}}, "elements": [{"id": "429:2029", "settings": {"eael_show_infobox_clickable": "", "eael_infobox_img_or_icon": "img", "eael_infobox_content_alignment": "center", "eael_infobox_img_type": "img-on-top", "icon_vertical_position": "top", "eael_infobox_image_resizer": {"unit": "px", "size": 568, "sizes": []}, "eael_infobox_image": {"url": "https://api.figma.com/v1/images/b077a7ae5a391a7f76814452557ec9f9502dbb8d", "id": "", "size": "", "alt": "", "source": "figma"}, "eael_infobox_img_shape": "radius", "eael_infobox_img_shape_radius": {"unit": "px", "top": "8", "right": "8", "bottom": "8", "left": "8", "isLinked": true}, "eael_infobox_img_margin": {"unit": "px", "top": "0", "right": "0", "bottom": "48", "left": "0", "isLinked": false}, "eael_section_infobox_container_bg": "#f2fcff", "eael_section_infobox_container_padding": {"unit": "px", "top": "24", "right": "24", "bottom": "24", "left": "24", "isLinked": true}, "eael_infobox_title": "250% Increase In Organic ", "eael_infobox_title_typography_typography": "custom", "eael_infobox_title_typography_font_family": "Inter Tight", "eael_infobox_title_typography_font_weight": "600", "eael_infobox_title_typography_font_size": {"unit": "px", "size": 32, "sizes": []}, "eael_infobox_title_typography_line_height": {"unit": "%", "size": 129.99999523162842, "sizes": []}, "eael_infobox_title_typography_text_transform": "none", "eael_infobox_title_typography_letter_spacing": {"unit": "PERCENT", "size": 0, "sizes": []}, "eael_infobox_title_color": "#091439", "eael_infobox_title_margin": {"unit": "px", "top": "0", "right": "0", "bottom": "8", "left": "0", "isLinked": false}, "eael_infobox_content_typography_hover_typography": "custom", "eael_infobox_content_typography_hover_font_family": "Inter Tight", "eael_infobox_content_typography_hover_font_weight": "400", "eael_infobox_content_typography_hover_font_size": {"unit": "px", "size": 18, "sizes": []}, "eael_infobox_content_typography_hover_line_height": {"unit": "%", "size": 160.0000023841858, "sizes": []}, "eael_infobox_content_typography_hover_text_transform": "none", "eael_infobox_content_typography_hover_letter_spacing": {"unit": "PERCENT", "size": 0, "sizes": []}, "eael_infobox_content_color": "#727686", "eael_infobox_content_margin": {"unit": "px", "top": "0", "right": "0", "bottom": "8", "left": "0", "isLinked": false}, "eael_infobox_text": "<p>SEO</p>", "typography_typography": "custom", "background_background": "classic", "background_color": "#f2fcff"}, "elements": [], "isInner": false, "elType": "widget", "widgetType": "eael-info-box"}, {"id": "429:2036", "settings": {"eael_show_infobox_clickable": "", "eael_infobox_img_or_icon": "img", "eael_infobox_content_alignment": "left", "eael_infobox_img_type": "img-on-top", "icon_vertical_position": "top", "eael_infobox_image_resizer": {"unit": "px", "size": 600, "sizes": []}, "eael_infobox_image": {"url": "https://api.figma.com/v1/images/96484f4559541ef32170c3eb312618e945109ebb", "id": "", "size": "", "alt": "", "source": "figma"}, "eael_infobox_img_shape": "radius", "eael_infobox_img_shape_radius": {"unit": "px", "top": "8", "right": "8", "bottom": "8", "left": "8", "isLinked": true}, "eael_infobox_img_margin": {"unit": "px", "top": "0", "right": "0", "bottom": "48", "left": "0", "isLinked": false}, "eael_section_infobox_container_bg": "#f6fce7", "eael_section_infobox_container_padding": {"unit": "px", "top": "24", "right": "24", "bottom": "24", "left": "24", "isLinked": true}, "eael_infobox_title": "$100K Revenue Generated With An Email Funnel", "eael_infobox_title_typography_typography": "custom", "eael_infobox_title_typography_font_family": "Inter Tight", "eael_infobox_title_typography_font_weight": "600", "eael_infobox_title_typography_font_size": {"unit": "px", "size": 32, "sizes": []}, "eael_infobox_title_typography_line_height": {"unit": "%", "size": 129.99999523162842, "sizes": []}, "eael_infobox_title_typography_text_transform": "none", "eael_infobox_title_typography_letter_spacing": {"unit": "PERCENT", "size": 0, "sizes": []}, "eael_infobox_title_color": "#091439", "eael_infobox_title_margin": {"unit": "px", "top": "0", "right": "0", "bottom": "8", "left": "0", "isLinked": false}, "eael_infobox_content_typography_hover_typography": "custom", "eael_infobox_content_typography_hover_font_family": "Inter Tight", "eael_infobox_content_typography_hover_font_weight": "400", "eael_infobox_content_typography_hover_font_size": {"unit": "px", "size": 18, "sizes": []}, "eael_infobox_content_typography_hover_line_height": {"unit": "%", "size": 160.0000023841858, "sizes": []}, "eael_infobox_content_typography_hover_text_transform": "none", "eael_infobox_content_typography_hover_letter_spacing": {"unit": "PERCENT", "size": 0, "sizes": []}, "eael_infobox_content_color": "#727686", "eael_infobox_content_margin": {"unit": "px", "top": "0", "right": "0", "bottom": "8", "left": "0", "isLinked": false}, "eael_infobox_text": "<p>Digital Marketing</p>", "typography_typography": "custom", "background_background": "classic", "background_color": "#f6fce7", "height": "min-height", "custom_height": {"unit": "px", "size": 597, "sizes": []}}, "elements": [], "isInner": false, "elType": "widget", "widgetType": "eael-info-box"}], "isInner": false, "elType": "container"}], "isInner": false, "elType": "container"}, {"id": "429:2041", "settings": {"flex_direction": "row", "content_width": "boxed", "width": {"unit": "px", "size": 178, "sizes": []}, "flex_justify_content": "center", "padding": {"unit": "px", "top": "0", "right": "0", "bottom": "0", "left": "0", "isLinked": true}, "height": "min-height", "custom_height": {"unit": "px", "size": 56, "sizes": []}}, "elements": [{"id": "429:2042", "settings": {"creative_button_text": "View All Case", "eael_creative_button_typography_typography": "custom", "eael_creative_button_typography_font_family": "Inter Tight", "eael_creative_button_typography_font_weight": "500", "eael_creative_button_typography_font_size": {"unit": "px", "size": 16, "sizes": []}, "eael_creative_button_typography_line_height": {"unit": "%", "size": 110.00000238418579, "sizes": []}, "eael_creative_button_typography_text_transform": "none", "eael_creative_button_typography_letter_spacing": {"unit": "PERCENT", "size": 0, "sizes": []}, "eael_creative_button_text_color": "#ffffff", "eael_creative_button_hover_text_color": "#ffffff", "eael_creative_button_icon_color": "#ffffff", "eael_creative_button_hover_icon_color": "#ffffff", "margin": {"unit": "px", "top": "0", "right": "10", "bottom": "0", "left": "0", "isLinked": false}, "eael_creative_button_icon_new": {"value": "fas fa-long-arrow-alt-right", "library": "fa-solid"}, "eael_creative_button_icon_alignment": "right", "eael_creative_button_icon_indent": {"unit": "px", "size": 10, "sizes": []}, "eael_creative_button_icon_size": {"unit": "px", "size": 24, "sizes": []}, "eael_creative_button_background_color": "#313c5e", "eael_creative_button_hover_background_color": "#313c5e", "eael_creative_button_border_radius": {"unit": "px", "size": "4", "sizes": []}, "typography_typography": "custom", "background_background": "gradient", "background_color": "#313c5e", "background_color_b": "#313c5e"}, "elements": [], "isInner": false, "elType": "widget", "widgetType": "eael-creative-button"}], "isInner": false, "elType": "container"}], "isInner": false, "elType": "container"}], "isInner": false, "elType": "container"}, {"id": "429:2048", "settings": {"flex_direction": "column", "content_width": "boxed", "width": {"unit": "px", "size": 1320, "sizes": []}, "padding": {"unit": "px", "top": "0", "right": "0", "bottom": "0", "left": "0", "isLinked": true}, "height": "min-height", "custom_height": {"unit": "px", "size": 1008, "sizes": []}}, "elements": [{"id": "429:2049", "settings": {"eael_dch_first_title": "Here’s What Our Clients Say", "eael_show_dch_icon_content": "", "eael_dch_last_title": "", "eael_dch_subtext": "", "eael_dch_first_title_typography_typography": "custom", "eael_dch_first_title_typography_font_family": "Inter Tight", "eael_dch_first_title_typography_font_weight": "600", "eael_dch_first_title_typography_font_size": {"unit": "px", "size": 48, "sizes": []}, "eael_dch_first_title_typography_line_height": {"unit": "%", "size": 129.99999523162842, "sizes": []}, "eael_dch_first_title_typography_text_transform": "none", "eael_dch_first_title_typography_letter_spacing": {"unit": "PERCENT", "size": 0, "sizes": []}, "eael_dch_base_title_color": "#091439", "eael_dch_dual_title_color": "#091439", "eael_dch_container_margin": {"unit": "px", "top": "0", "right": "0", "bottom": "0", "left": "0", "isLinked": false}, "typography_typography": "custom", "typography_font_family": "Inter Tight", "typography_font_weight": "600", "typography_font_size": {"unit": "px", "size": 48, "sizes": []}, "typography_line_height": {"unit": "%", "size": 129.99999523162842, "sizes": []}, "title_color": "#091439", "height": "min-height", "custom_height": {"unit": "px", "size": 62, "sizes": []}}, "elements": [], "isInner": false, "elType": "widget", "widgetType": "eael-dual-color-header"}, {"id": "429:2050", "settings": {"flex_direction": "column", "content_width": "boxed", "width": {"unit": "px", "size": 1320, "sizes": []}, "padding": {"unit": "px", "top": "0", "right": "0", "bottom": "0", "left": "0", "isLinked": true}, "height": "min-height", "custom_height": {"unit": "px", "size": 890, "sizes": []}}, "elements": [{"id": "429:2051", "settings": {"flex_direction": "row", "content_width": "boxed", "width": {"unit": "px", "size": 1320, "sizes": []}, "flex_justify_content": "center", "padding": {"unit": "px", "top": "0", "right": "0", "bottom": "0", "left": "0", "isLinked": true}, "height": "min-height", "custom_height": {"unit": "px", "size": 802, "sizes": []}}, "elements": [{"id": "429:2052", "settings": {"flex_direction": "column", "content_width": "boxed", "width": {"unit": "px", "size": 424, "sizes": []}, "padding": {"unit": "px", "top": "0", "right": "0", "bottom": "0", "left": "0", "isLinked": true}, "height": "min-height", "custom_height": {"unit": "px", "size": 744, "sizes": []}}, "elements": [{"id": "429:2053", "settings": {"eael_testimonial_quotation_color": "", "eael_testimonial_quotation_top": {"unit": "%", "size": "", "sizes": []}, "eael_testimonial_quotation_right": {"unit": "%", "size": "", "sizes": []}, "eael_testimonial_style": "content-bottom-icon-title-inline", "eael_testimonial_user_display_block": "yes", "eael_testimonial_background": "#fffaf4", "eael_testimonial_name": "Name", "eael_testimonial_name_typography_typography": "custom", "eael_testimonial_name_typography_font_family": "Inter Tight", "eael_testimonial_name_typography_font_weight": "600", "eael_testimonial_name_typography_font_size": {"unit": "px", "size": 18, "sizes": []}, "eael_testimonial_name_typography_line_height": {"unit": "%", "size": 160.0000023841858, "sizes": []}, "eael_testimonial_name_typography_text_transform": "none", "eael_testimonial_name_typography_letter_spacing": {"unit": "PERCENT", "size": 0, "sizes": []}, "eael_testimonial_name_color": "#091439", "margin": {"unit": "px", "top": "0", "right": "0", "bottom": "0", "left": "0", "isLinked": false}, "eael_testimonial_company_title": "Company Name ", "eael_testimonial_position_typography_typography": "custom", "eael_testimonial_position_typography_font_family": "Inter Tight", "eael_testimonial_position_typography_font_weight": "400", "eael_testimonial_position_typography_font_size": {"unit": "px", "size": 16, "sizes": []}, "eael_testimonial_position_typography_line_height": {"unit": "%", "size": 139.9999976158142, "sizes": []}, "eael_testimonial_position_typography_text_transform": "none", "eael_testimonial_position_typography_letter_spacing": {"unit": "PERCENT", "size": 0, "sizes": []}, "eael_testimonial_company_color": "#727686", "eael_testimonial_description": "The team’s expertise in SEO completely turned our website around. Within three months, our organic traffic skyrocketed, & we ranked on the first page for our target keywords.", "eael_testimonial_description_typography_typography": "custom", "eael_testimonial_description_typography_font_family": "Inter Tight", "eael_testimonial_description_typography_font_weight": "400", "eael_testimonial_description_typography_font_size": {"unit": "px", "size": 18, "sizes": []}, "eael_testimonial_description_typography_line_height": {"unit": "%", "size": 160.0000023841858, "sizes": []}, "eael_testimonial_description_typography_text_transform": "none", "eael_testimonial_description_typography_letter_spacing": {"unit": "PERCENT", "size": 0, "sizes": []}, "eael_testimonial_description_color": "#727686", "eael_testimonial_description_margin": {"unit": "px", "top": "0", "right": "0", "bottom": "24", "left": "0", "isLinked": false}, "eael_testimonial_image_width": {"unit": "px", "size": 52, "sizes": []}, "eael_testimonial_max_image_width": {"unit": "%", "size": 52, "sizes": []}, "eael_testimonial_image_rounded": "testimonial-avatar-rounded", "eael_testimonial_image_margin": {"unit": "px", "top": "0", "right": "16", "bottom": "0", "left": "0", "isLinked": false}, "eael_testimonial_rating_item_size": {"unit": "px", "size": 24, "sizes": []}, "eael_testimonial_rating_margin": {"unit": "px", "top": "0", "right": "0", "bottom": "24", "left": "0", "isLinked": false}, "eael_testimonial_rating_item_distance": {"unit": "px", "top": "0", "right": "7.5", "bottom": "0", "left": "0", "isLinked": false}, "typography_typography": "custom", "background_background": "classic", "background_color": "#fffaf4", "border_radius": {"unit": "px", "top": "8", "right": "8", "bottom": "8", "left": "8", "isLinked": true}, "height": "min-height", "custom_height": {"unit": "px", "size": 360, "sizes": []}}, "elements": [], "isInner": false, "elType": "widget", "widgetType": "eael-testimonial"}, {"id": "429:2067", "settings": {"eael_testimonial_quotation_color": "", "eael_testimonial_quotation_top": {"unit": "%", "size": "", "sizes": []}, "eael_testimonial_quotation_right": {"unit": "%", "size": "", "sizes": []}, "eael_testimonial_style": "content-bottom-icon-title-inline", "eael_testimonial_user_display_block": "yes", "eael_testimonial_background": "#f4faff", "eael_testimonial_name": "Name", "eael_testimonial_name_typography_typography": "custom", "eael_testimonial_name_typography_font_family": "Inter Tight", "eael_testimonial_name_typography_font_weight": "600", "eael_testimonial_name_typography_font_size": {"unit": "px", "size": 18, "sizes": []}, "eael_testimonial_name_typography_line_height": {"unit": "%", "size": 160.0000023841858, "sizes": []}, "eael_testimonial_name_typography_text_transform": "none", "eael_testimonial_name_typography_letter_spacing": {"unit": "PERCENT", "size": 0, "sizes": []}, "eael_testimonial_name_color": "#091439", "margin": {"unit": "px", "top": "0", "right": "0", "bottom": "0", "left": "0", "isLinked": false}, "eael_testimonial_company_title": "Company Name ", "eael_testimonial_position_typography_typography": "custom", "eael_testimonial_position_typography_font_family": "Inter Tight", "eael_testimonial_position_typography_font_weight": "400", "eael_testimonial_position_typography_font_size": {"unit": "px", "size": 16, "sizes": []}, "eael_testimonial_position_typography_line_height": {"unit": "%", "size": 139.9999976158142, "sizes": []}, "eael_testimonial_position_typography_text_transform": "none", "eael_testimonial_position_typography_letter_spacing": {"unit": "PERCENT", "size": 0, "sizes": []}, "eael_testimonial_company_color": "#727686", "eael_testimonial_description": "We partnered with them for a social media overhaul, and the results were incredible. Their targeted campaigns and engaging content increased our followers by 200%.", "eael_testimonial_description_typography_typography": "custom", "eael_testimonial_description_typography_font_family": "Inter Tight", "eael_testimonial_description_typography_font_weight": "400", "eael_testimonial_description_typography_font_size": {"unit": "px", "size": 18, "sizes": []}, "eael_testimonial_description_typography_line_height": {"unit": "%", "size": 160.0000023841858, "sizes": []}, "eael_testimonial_description_typography_text_transform": "none", "eael_testimonial_description_typography_letter_spacing": {"unit": "PERCENT", "size": 0, "sizes": []}, "eael_testimonial_description_color": "#727686", "eael_testimonial_description_margin": {"unit": "px", "top": "0", "right": "0", "bottom": "24", "left": "0", "isLinked": false}, "eael_testimonial_image_width": {"unit": "px", "size": 52, "sizes": []}, "eael_testimonial_max_image_width": {"unit": "%", "size": 52, "sizes": []}, "eael_testimonial_image_rounded": "testimonial-avatar-rounded", "eael_testimonial_image_margin": {"unit": "px", "top": "0", "right": "16", "bottom": "0", "left": "0", "isLinked": false}, "eael_testimonial_rating_item_size": {"unit": "px", "size": 24, "sizes": []}, "eael_testimonial_rating_margin": {"unit": "px", "top": "0", "right": "0", "bottom": "24", "left": "0", "isLinked": false}, "eael_testimonial_rating_item_distance": {"unit": "px", "top": "0", "right": "7.5", "bottom": "0", "left": "0", "isLinked": false}, "typography_typography": "custom", "background_background": "classic", "background_color": "#f4faff", "border_radius": {"unit": "px", "top": "8", "right": "8", "bottom": "8", "left": "8", "isLinked": true}, "height": "min-height", "custom_height": {"unit": "px", "size": 360, "sizes": []}}, "elements": [], "isInner": false, "elType": "widget", "widgetType": "eael-testimonial"}], "isInner": false, "elType": "container"}, {"id": "429:2081", "settings": {"flex_direction": "column", "content_width": "boxed", "width": {"unit": "px", "size": 424, "sizes": []}, "padding": {"unit": "px", "top": "0", "right": "0", "bottom": "0", "left": "0", "isLinked": true}}, "elements": [{"id": "429:2082", "settings": {"eael_testimonial_quotation_color": "", "eael_testimonial_quotation_top": {"unit": "%", "size": "", "sizes": []}, "eael_testimonial_quotation_right": {"unit": "%", "size": "", "sizes": []}, "eael_testimonial_style": "content-bottom-icon-title-inline", "eael_testimonial_user_display_block": "yes", "eael_testimonial_background": "#f4f4ff", "eael_testimonial_name": "Name", "eael_testimonial_name_typography_typography": "custom", "eael_testimonial_name_typography_font_family": "Inter Tight", "eael_testimonial_name_typography_font_weight": "600", "eael_testimonial_name_typography_font_size": {"unit": "px", "size": 18, "sizes": []}, "eael_testimonial_name_typography_line_height": {"unit": "%", "size": 160.0000023841858, "sizes": []}, "eael_testimonial_name_typography_text_transform": "none", "eael_testimonial_name_typography_letter_spacing": {"unit": "PERCENT", "size": 0, "sizes": []}, "eael_testimonial_name_color": "#091439", "margin": {"unit": "px", "top": "0", "right": "0", "bottom": "0", "left": "0", "isLinked": false}, "eael_testimonial_company_title": "Company Name ", "eael_testimonial_position_typography_typography": "custom", "eael_testimonial_position_typography_font_family": "Inter Tight", "eael_testimonial_position_typography_font_weight": "400", "eael_testimonial_position_typography_font_size": {"unit": "px", "size": 16, "sizes": []}, "eael_testimonial_position_typography_line_height": {"unit": "%", "size": 139.9999976158142, "sizes": []}, "eael_testimonial_position_typography_text_transform": "none", "eael_testimonial_position_typography_letter_spacing": {"unit": "PERCENT", "size": 0, "sizes": []}, "eael_testimonial_company_color": "#727686", "eael_testimonial_description": "From rebranding our business to launching a new website, their creativity and professionalism were outstanding. They delivered a stunning design and a seamless user experience.", "eael_testimonial_description_typography_typography": "custom", "eael_testimonial_description_typography_font_family": "Inter Tight", "eael_testimonial_description_typography_font_weight": "400", "eael_testimonial_description_typography_font_size": {"unit": "px", "size": 18, "sizes": []}, "eael_testimonial_description_typography_line_height": {"unit": "%", "size": 160.0000023841858, "sizes": []}, "eael_testimonial_description_typography_text_transform": "none", "eael_testimonial_description_typography_letter_spacing": {"unit": "PERCENT", "size": 0, "sizes": []}, "eael_testimonial_description_color": "#727686", "eael_testimonial_description_margin": {"unit": "px", "top": "0", "right": "0", "bottom": "24", "left": "0", "isLinked": false}, "eael_testimonial_image_width": {"unit": "px", "size": 52, "sizes": []}, "eael_testimonial_max_image_width": {"unit": "%", "size": 52, "sizes": []}, "eael_testimonial_image_rounded": "testimonial-avatar-rounded", "eael_testimonial_image_margin": {"unit": "px", "top": "0", "right": "16", "bottom": "0", "left": "0", "isLinked": false}, "eael_testimonial_rating_item_size": {"unit": "px", "size": 24, "sizes": []}, "eael_testimonial_rating_margin": {"unit": "px", "top": "0", "right": "0", "bottom": "24", "left": "0", "isLinked": false}, "eael_testimonial_rating_item_distance": {"unit": "px", "top": "0", "right": "7.5", "bottom": "0", "left": "0", "isLinked": false}, "typography_typography": "custom", "background_background": "classic", "background_color": "#f4f4ff", "border_radius": {"unit": "px", "top": "8", "right": "8", "bottom": "8", "left": "8", "isLinked": true}, "height": "min-height", "custom_height": {"unit": "px", "size": 389, "sizes": []}}, "elements": [], "isInner": false, "elType": "widget", "widgetType": "eael-testimonial"}, {"id": "429:2096", "settings": {"eael_testimonial_quotation_color": "", "eael_testimonial_quotation_top": {"unit": "%", "size": "", "sizes": []}, "eael_testimonial_quotation_right": {"unit": "%", "size": "", "sizes": []}, "eael_testimonial_style": "content-bottom-icon-title-inline", "eael_testimonial_user_display_block": "yes", "eael_testimonial_background": "#f2faf3", "eael_testimonial_name": "Name", "eael_testimonial_name_typography_typography": "custom", "eael_testimonial_name_typography_font_family": "Inter Tight", "eael_testimonial_name_typography_font_weight": "600", "eael_testimonial_name_typography_font_size": {"unit": "px", "size": 18, "sizes": []}, "eael_testimonial_name_typography_line_height": {"unit": "%", "size": 160.0000023841858, "sizes": []}, "eael_testimonial_name_typography_text_transform": "none", "eael_testimonial_name_typography_letter_spacing": {"unit": "PERCENT", "size": 0, "sizes": []}, "eael_testimonial_name_color": "#091439", "margin": {"unit": "px", "top": "0", "right": "0", "bottom": "0", "left": "0", "isLinked": false}, "eael_testimonial_company_title": "Company Name ", "eael_testimonial_position_typography_typography": "custom", "eael_testimonial_position_typography_font_family": "Inter Tight", "eael_testimonial_position_typography_font_weight": "400", "eael_testimonial_position_typography_font_size": {"unit": "px", "size": 16, "sizes": []}, "eael_testimonial_position_typography_line_height": {"unit": "%", "size": 139.9999976158142, "sizes": []}, "eael_testimonial_position_typography_text_transform": "none", "eael_testimonial_position_typography_letter_spacing": {"unit": "PERCENT", "size": 0, "sizes": []}, "eael_testimonial_company_color": "#727686", "eael_testimonial_description": "Their expertise helped us create a winning email funnel that brought in $50K in revenue within the first quarter. They understood our business goals and delivered beyond our expectations!", "eael_testimonial_description_typography_typography": "custom", "eael_testimonial_description_typography_font_family": "Inter Tight", "eael_testimonial_description_typography_font_weight": "400", "eael_testimonial_description_typography_font_size": {"unit": "px", "size": 18, "sizes": []}, "eael_testimonial_description_typography_line_height": {"unit": "%", "size": 160.0000023841858, "sizes": []}, "eael_testimonial_description_typography_text_transform": "none", "eael_testimonial_description_typography_letter_spacing": {"unit": "PERCENT", "size": 0, "sizes": []}, "eael_testimonial_description_color": "#727686", "eael_testimonial_description_margin": {"unit": "px", "top": "0", "right": "0", "bottom": "24", "left": "0", "isLinked": false}, "eael_testimonial_image_width": {"unit": "px", "size": 52, "sizes": []}, "eael_testimonial_max_image_width": {"unit": "%", "size": 52, "sizes": []}, "eael_testimonial_image_rounded": "testimonial-avatar-rounded", "eael_testimonial_image_margin": {"unit": "px", "top": "0", "right": "16", "bottom": "0", "left": "0", "isLinked": false}, "eael_testimonial_rating_item_size": {"unit": "px", "size": 24, "sizes": []}, "eael_testimonial_rating_margin": {"unit": "px", "top": "0", "right": "0", "bottom": "24", "left": "0", "isLinked": false}, "eael_testimonial_rating_item_distance": {"unit": "px", "top": "0", "right": "7.5", "bottom": "0", "left": "0", "isLinked": false}, "typography_typography": "custom", "background_background": "classic", "background_color": "#f2faf3", "border_radius": {"unit": "px", "top": "8", "right": "8", "bottom": "8", "left": "8", "isLinked": true}, "height": "min-height", "custom_height": {"unit": "px", "size": 389, "sizes": []}}, "elements": [], "isInner": false, "elType": "widget", "widgetType": "eael-testimonial"}], "isInner": false, "elType": "container"}, {"id": "429:2110", "settings": {"flex_direction": "column", "content_width": "boxed", "width": {"unit": "px", "size": 424, "sizes": []}, "padding": {"unit": "px", "top": "0", "right": "0", "bottom": "0", "left": "0", "isLinked": true}, "height": "min-height", "custom_height": {"unit": "px", "size": 773, "sizes": []}}, "elements": [{"id": "429:2111", "settings": {"eael_testimonial_quotation_color": "", "eael_testimonial_quotation_top": {"unit": "%", "size": "", "sizes": []}, "eael_testimonial_quotation_right": {"unit": "%", "size": "", "sizes": []}, "eael_testimonial_style": "content-bottom-icon-title-inline", "eael_testimonial_user_display_block": "yes", "eael_testimonial_background": "#fff4fb", "eael_testimonial_name": "Name", "eael_testimonial_name_typography_typography": "custom", "eael_testimonial_name_typography_font_family": "Inter Tight", "eael_testimonial_name_typography_font_weight": "600", "eael_testimonial_name_typography_font_size": {"unit": "px", "size": 18, "sizes": []}, "eael_testimonial_name_typography_line_height": {"unit": "%", "size": 160.0000023841858, "sizes": []}, "eael_testimonial_name_typography_text_transform": "none", "eael_testimonial_name_typography_letter_spacing": {"unit": "PERCENT", "size": 0, "sizes": []}, "eael_testimonial_name_color": "#091439", "margin": {"unit": "px", "top": "0", "right": "0", "bottom": "0", "left": "0", "isLinked": false}, "eael_testimonial_company_title": "Company Name ", "eael_testimonial_position_typography_typography": "custom", "eael_testimonial_position_typography_font_family": "Inter Tight", "eael_testimonial_position_typography_font_weight": "400", "eael_testimonial_position_typography_font_size": {"unit": "px", "size": 16, "sizes": []}, "eael_testimonial_position_typography_line_height": {"unit": "%", "size": 139.9999976158142, "sizes": []}, "eael_testimonial_position_typography_text_transform": "none", "eael_testimonial_position_typography_letter_spacing": {"unit": "PERCENT", "size": 0, "sizes": []}, "eael_testimonial_company_color": "#727686", "eael_testimonial_description": "The team delivered exceptional results with their custom app development services. The app was not only visually appealing but also intuitive driving thousands of downloads.", "eael_testimonial_description_typography_typography": "custom", "eael_testimonial_description_typography_font_family": "Inter Tight", "eael_testimonial_description_typography_font_weight": "400", "eael_testimonial_description_typography_font_size": {"unit": "px", "size": 18, "sizes": []}, "eael_testimonial_description_typography_line_height": {"unit": "%", "size": 160.0000023841858, "sizes": []}, "eael_testimonial_description_typography_text_transform": "none", "eael_testimonial_description_typography_letter_spacing": {"unit": "PERCENT", "size": 0, "sizes": []}, "eael_testimonial_description_color": "#727686", "eael_testimonial_description_margin": {"unit": "px", "top": "0", "right": "0", "bottom": "24", "left": "0", "isLinked": false}, "eael_testimonial_image_width": {"unit": "px", "size": 52, "sizes": []}, "eael_testimonial_max_image_width": {"unit": "%", "size": 52, "sizes": []}, "eael_testimonial_image_rounded": "testimonial-avatar-rounded", "eael_testimonial_image_margin": {"unit": "px", "top": "0", "right": "16", "bottom": "0", "left": "0", "isLinked": false}, "eael_testimonial_rating_item_size": {"unit": "px", "size": 24, "sizes": []}, "eael_testimonial_rating_margin": {"unit": "px", "top": "0", "right": "0", "bottom": "24", "left": "0", "isLinked": false}, "eael_testimonial_rating_item_distance": {"unit": "px", "top": "0", "right": "7.5", "bottom": "0", "left": "0", "isLinked": false}, "typography_typography": "custom", "background_background": "classic", "background_color": "#fff4fb", "border_radius": {"unit": "px", "top": "8", "right": "8", "bottom": "8", "left": "8", "isLinked": true}, "height": "min-height", "custom_height": {"unit": "px", "size": 360, "sizes": []}}, "elements": [], "isInner": false, "elType": "widget", "widgetType": "eael-testimonial"}, {"id": "429:2125", "settings": {"eael_testimonial_quotation_color": "", "eael_testimonial_quotation_top": {"unit": "%", "size": "", "sizes": []}, "eael_testimonial_quotation_right": {"unit": "%", "size": "", "sizes": []}, "eael_testimonial_style": "content-bottom-icon-title-inline", "eael_testimonial_user_display_block": "yes", "eael_testimonial_background": "#faf6f2", "eael_testimonial_name": "Name", "eael_testimonial_name_typography_typography": "custom", "eael_testimonial_name_typography_font_family": "Inter Tight", "eael_testimonial_name_typography_font_weight": "600", "eael_testimonial_name_typography_font_size": {"unit": "px", "size": 18, "sizes": []}, "eael_testimonial_name_typography_line_height": {"unit": "%", "size": 160.0000023841858, "sizes": []}, "eael_testimonial_name_typography_text_transform": "none", "eael_testimonial_name_typography_letter_spacing": {"unit": "PERCENT", "size": 0, "sizes": []}, "eael_testimonial_name_color": "#091439", "margin": {"unit": "px", "top": "0", "right": "0", "bottom": "0", "left": "0", "isLinked": false}, "eael_testimonial_company_title": "Company Name ", "eael_testimonial_position_typography_typography": "custom", "eael_testimonial_position_typography_font_family": "Inter Tight", "eael_testimonial_position_typography_font_weight": "400", "eael_testimonial_position_typography_font_size": {"unit": "px", "size": 16, "sizes": []}, "eael_testimonial_position_typography_line_height": {"unit": "%", "size": 139.9999976158142, "sizes": []}, "eael_testimonial_position_typography_text_transform": "none", "eael_testimonial_position_typography_letter_spacing": {"unit": "PERCENT", "size": 0, "sizes": []}, "eael_testimonial_company_color": "#727686", "eael_testimonial_description": "Their AI-powered solutions helped us streamline our marketing efforts. The personalized email campaigns and predictive analytics boosted our conversion rates by 45%.", "eael_testimonial_description_typography_typography": "custom", "eael_testimonial_description_typography_font_family": "Inter Tight", "eael_testimonial_description_typography_font_weight": "400", "eael_testimonial_description_typography_font_size": {"unit": "px", "size": 18, "sizes": []}, "eael_testimonial_description_typography_line_height": {"unit": "%", "size": 160.0000023841858, "sizes": []}, "eael_testimonial_description_typography_text_transform": "none", "eael_testimonial_description_typography_letter_spacing": {"unit": "PERCENT", "size": 0, "sizes": []}, "eael_testimonial_description_color": "#727686", "eael_testimonial_description_margin": {"unit": "px", "top": "0", "right": "0", "bottom": "24", "left": "0", "isLinked": false}, "eael_testimonial_image_width": {"unit": "px", "size": 52, "sizes": []}, "eael_testimonial_max_image_width": {"unit": "%", "size": 52, "sizes": []}, "eael_testimonial_image_rounded": "testimonial-avatar-rounded", "eael_testimonial_image_margin": {"unit": "px", "top": "0", "right": "16", "bottom": "0", "left": "0", "isLinked": false}, "eael_testimonial_rating_item_size": {"unit": "px", "size": 24, "sizes": []}, "eael_testimonial_rating_margin": {"unit": "px", "top": "0", "right": "0", "bottom": "24", "left": "0", "isLinked": false}, "eael_testimonial_rating_item_distance": {"unit": "px", "top": "0", "right": "7.5", "bottom": "0", "left": "0", "isLinked": false}, "typography_typography": "custom", "background_background": "classic", "background_color": "#faf6f2", "border_radius": {"unit": "px", "top": "8", "right": "8", "bottom": "8", "left": "8", "isLinked": true}, "height": "min-height", "custom_height": {"unit": "px", "size": 389, "sizes": []}}, "elements": [], "isInner": false, "elType": "widget", "widgetType": "eael-testimonial"}], "isInner": false, "elType": "container"}], "isInner": false, "elType": "container"}, {"id": "429:2139", "settings": {"flex_direction": "row", "content_width": "boxed", "width": {"unit": "px", "size": 171, "sizes": []}, "flex_justify_content": "center", "padding": {"unit": "px", "top": "0", "right": "0", "bottom": "0", "left": "0", "isLinked": true}, "height": "min-height", "custom_height": {"unit": "px", "size": 56, "sizes": []}}, "elements": [{"id": "429:2140", "settings": {"creative_button_text": "View Review", "eael_creative_button_typography_typography": "custom", "eael_creative_button_typography_font_family": "Inter Tight", "eael_creative_button_typography_font_weight": "500", "eael_creative_button_typography_font_size": {"unit": "px", "size": 16, "sizes": []}, "eael_creative_button_typography_line_height": {"unit": "%", "size": 110.00000238418579, "sizes": []}, "eael_creative_button_typography_text_transform": "none", "eael_creative_button_typography_letter_spacing": {"unit": "PERCENT", "size": 0, "sizes": []}, "eael_creative_button_text_color": "#ffffff", "eael_creative_button_hover_text_color": "#ffffff", "eael_creative_button_icon_color": "#ffffff", "eael_creative_button_hover_icon_color": "#ffffff", "margin": {"unit": "px", "top": "0", "right": "10", "bottom": "0", "left": "0", "isLinked": false}, "eael_creative_button_icon_new": {"value": "fas fa-long-arrow-alt-right", "library": "fa-solid"}, "eael_creative_button_icon_alignment": "right", "eael_creative_button_icon_indent": {"unit": "px", "size": 10, "sizes": []}, "eael_creative_button_icon_size": {"unit": "px", "size": 24, "sizes": []}, "eael_creative_button_background_color": "#313c5e", "eael_creative_button_hover_background_color": "#313c5e", "eael_creative_button_border_radius": {"unit": "px", "size": "4", "sizes": []}, "typography_typography": "custom", "background_background": "gradient", "background_color": "#313c5e", "background_color_b": "#313c5e"}, "elements": [], "isInner": false, "elType": "widget", "widgetType": "eael-creative-button"}], "isInner": false, "elType": "container"}], "isInner": false, "elType": "container"}], "isInner": false, "elType": "container"}, {"id": "429:2146", "settings": {"flex_direction": "row", "content_width": "boxed", "width": {"unit": "px", "size": 1320, "sizes": []}, "flex_justify_content": "center", "padding": {"unit": "px", "top": "71", "right": "56", "bottom": "71", "left": "56", "isLinked": false}, "background_background": "classic", "background_color": "#e2ddff", "border_radius": {"unit": "px", "top": "16", "right": "16", "bottom": "16", "left": "16", "isLinked": true}, "height": "min-height", "custom_height": {"unit": "px", "size": 320, "sizes": []}}, "elements": [{"id": "429:2147", "settings": {"flex_direction": "column", "content_width": "boxed", "width": {"unit": "px", "size": 397, "sizes": []}, "padding": {"unit": "px", "top": "0", "right": "0", "bottom": "0", "left": "0", "isLinked": true}, "height": "min-height", "custom_height": {"unit": "px", "size": 178, "sizes": []}}, "elements": [{"id": "429:2148", "settings": {"title": "We Help to Build the Business", "header_size": "h2", "t": "flex-start", "typography_typography": "custom", "typography_font_family": "Inter Tight", "typography_font_weight": "600", "typography_font_size": {"unit": "px", "size": 40, "sizes": []}, "typography_line_height": {"unit": "%", "size": 129.99999523162842, "sizes": []}, "title_color": "#091439", "height": "min-height", "custom_height": {"unit": "px", "size": 104, "sizes": []}}, "elements": [], "isInner": false, "elType": "widget", "widgetType": "heading"}, {"id": "429:2149", "settings": {"editor": "<p>Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt </p>", "typography_typography": "custom", "typography_font_family": "Inter Tight", "typography_font_weight": "400", "typography_font_size": {"unit": "px", "size": 18, "sizes": []}, "typography_line_height": {"unit": "%", "size": 160.0000023841858, "sizes": []}, "title_color": "#727686", "height": "min-height", "custom_height": {"unit": "px", "size": 58, "sizes": []}}, "elements": [], "isInner": false, "elType": "widget", "widgetType": "text-editor"}], "isInner": false, "elType": "container"}, {"id": "429:2150", "settings": {"flex_direction": "column", "content_width": "boxed", "width": {"unit": "px", "size": 397, "sizes": []}, "padding": {"unit": "px", "top": "0", "right": "0", "bottom": "0", "left": "0", "isLinked": true}, "height": "min-height", "custom_height": {"unit": "px", "size": 164, "sizes": []}}, "elements": [{"id": "429:2151", "settings": {"eael_feature_list_connector": "", "eael_feature_list": [{"eael_feature_list_title": "Solution Design And Optimization", "eael_feature_list_content": "", "eael_feature_list_icon_new": {"value": "fas fa-long-arrow-alt-right", "library": "fa-solid"}}, {"eael_feature_list_title": "Custom AI Model Development", "eael_feature_list_content": "", "eael_feature_list_icon_new": {"value": "fas fa-long-arrow-alt-right", "library": "fa-solid"}}, {"eael_feature_list_title": "Scalable And Efficient Systems", "eael_feature_list_content": "", "eael_feature_list_icon_new": {"value": "fas fa-long-arrow-alt-right", "library": "fa-solid"}}, {"eael_feature_list_title": "Integration With Existing Systems", "eael_feature_list_content": "", "eael_feature_list_icon_new": {"value": "fas fa-long-arrow-alt-right", "library": "fa-solid"}}], "eael_infobox_button_icon_rotate": {"unit": "deg", "size": -240, "sizes": []}, "eael_feature_list_title_typography_typography": "custom", "eael_feature_list_title_typography_font_family": "Inter Tight", "eael_feature_list_title_typography_font_weight": "400", "eael_feature_list_title_typography_font_size": {"unit": "px", "size": 18, "sizes": []}, "eael_feature_list_title_typography_line_height": {"unit": "%", "size": 160.0000023841858, "sizes": []}, "eael_feature_list_title_typography_text_transform": "none", "eael_feature_list_title_typography_letter_spacing": {"unit": "PERCENT", "size": 0, "sizes": []}, "eael_feature_list_title_color": "#091439", "margin": {"unit": "px", "top": "0", "right": "8", "bottom": "0", "left": "0", "isLinked": false}, "eael_feature_list_icon_color": "", "eael_feature_list_icon_size": {"unit": "px", "size": 24, "sizes": []}, "eael_feature_list_icon_circle_size": {"unit": "px", "size": 24, "sizes": []}, "eael_feature_list_icon_space": {"unit": "px", "size": "8", "sizes": []}, "eael_feature_list_icon_padding": {"unit": "px", "top": "6", "right": "3", "bottom": "6", "left": "3", "isLinked": false}, "eael_feature_list_icon_background_color": "#e2ddff", "eael_feature_list_icon_background_background": "classic", "typography_typography": "custom"}, "elements": [], "isInner": false, "elType": "widget", "widgetType": "eael-feature-list"}], "isInner": false, "elType": "container"}], "isInner": false, "elType": "container"}, {"id": "429:2168", "settings": {"flex_direction": "column", "content_width": "boxed", "width": {"unit": "px", "size": 1319, "sizes": []}, "padding": {"unit": "px", "top": "0", "right": "0", "bottom": "0", "left": "0", "isLinked": true}, "height": "min-height", "custom_height": {"unit": "px", "size": 595, "sizes": []}}, "elements": [{"id": "429:2169", "settings": {"typography_typography": "custom", "typography_font_family": "Inter Tight", "typography_font_weight": "600", "typography_font_size": {"unit": "px", "size": 48, "sizes": []}, "typography_line_height": {"unit": "%", "size": 129.99999523162842, "sizes": []}, "title_color": "#091439", "height": "min-height", "custom_height": {"unit": "px", "size": 62, "sizes": []}}, "elements": [], "isInner": false, "elType": "widget", "widgetType": "dual-color-heading"}, {"id": "429:2170", "settings": {"flex_direction": "row", "content_width": "boxed", "width": {"unit": "px", "size": 1319, "sizes": []}, "padding": {"unit": "px", "top": "0", "right": "0", "bottom": "0", "left": "0", "isLinked": true}, "height": "min-height", "custom_height": {"unit": "px", "size": 477, "sizes": []}}, "elements": [{"id": "429:2171", "settings": {"eael_show_infobox_clickable": "", "eael_infobox_img_or_icon": "img", "eael_infobox_content_alignment": "left", "eael_infobox_img_type": "img-on-top", "icon_vertical_position": "top", "eael_infobox_image_resizer": {"unit": "px", "size": 92, "sizes": []}, "eael_infobox_image": {"url": "https://api.figma.com/v1/images/7232c393b30c82082b8df18676de35dffd2a2f84", "id": "", "size": "", "alt": "", "source": "figma"}, "eael_infobox_img_margin": {"unit": "px", "top": "0", "right": "0", "bottom": "48", "left": "0", "isLinked": false}, "eael_section_infobox_container_bg": "#fff4fb", "eael_section_infobox_container_padding": {"unit": "px", "top": "101", "right": "48", "bottom": "101", "left": "48", "isLinked": false}, "eael_infobox_title": "Innovation Award", "eael_infobox_title_typography_typography": "custom", "eael_infobox_title_typography_font_family": "Inter Tight", "eael_infobox_title_typography_font_weight": "600", "eael_infobox_title_typography_font_size": {"unit": "px", "size": 24, "sizes": []}, "eael_infobox_title_typography_line_height": {"unit": "%", "size": 129.99999523162842, "sizes": []}, "eael_infobox_title_typography_text_transform": "none", "eael_infobox_title_typography_letter_spacing": {"unit": "PERCENT", "size": 0, "sizes": []}, "eael_infobox_title_color": "#091439", "eael_infobox_title_margin": {"unit": "px", "top": "0", "right": "0", "bottom": "16", "left": "0", "isLinked": false}, "eael_infobox_content_typography_hover_typography": "custom", "eael_infobox_content_typography_hover_font_family": "Inter Tight", "eael_infobox_content_typography_hover_font_weight": "400", "eael_infobox_content_typography_hover_font_size": {"unit": "px", "size": 18, "sizes": []}, "eael_infobox_content_typography_hover_line_height": {"unit": "%", "size": 160.0000023841858, "sizes": []}, "eael_infobox_content_typography_hover_text_transform": "none", "eael_infobox_content_typography_hover_letter_spacing": {"unit": "PERCENT", "size": 0, "sizes": []}, "eael_infobox_content_color": "#727686", "eael_infobox_content_margin": {"unit": "px", "top": "0", "right": "0", "bottom": "16", "left": "0", "isLinked": false}, "eael_infobox_text": "<p>Recognized for pioneering digital transformation through impactful innovation and leading ideas.</p>", "typography_typography": "custom", "background_background": "classic", "background_color": "#fff4fb", "height": "min-height", "custom_height": {"unit": "px", "size": 476, "sizes": []}}, "elements": [], "isInner": false, "elType": "widget", "widgetType": "eael-info-box"}, {"id": "429:2177", "settings": {"flex_direction": "column", "content_width": "boxed", "width": {"unit": "px", "size": 423, "sizes": []}, "padding": {"unit": "px", "top": "0", "right": "0", "bottom": "0", "left": "0", "isLinked": true}}, "elements": [{"id": "429:2178", "settings": {"eael_show_infobox_clickable": "", "eael_infobox_img_or_icon": "img", "eael_infobox_content_alignment": "left", "eael_infobox_img_type": "img-on-left", "icon_vertical_position": "top", "eael_infobox_image_resizer": {"unit": "px", "size": 56, "sizes": []}, "eael_infobox_image": {"url": "https://api.figma.com/v1/images/8a8d69aedf060668219c8652e33f95f666e13f0f", "id": "", "size": "", "alt": "", "source": "figma"}, "eael_infobox_img_margin": {"unit": "px", "top": "0", "right": "32", "bottom": "0", "left": "0", "isLinked": false}, "eael_section_infobox_container_bg": "#fffaf4", "eael_section_infobox_container_padding": {"unit": "px", "top": "61", "right": "36", "bottom": "61", "left": "36", "isLinked": false}, "eael_infobox_title": "Best Agency 2024", "eael_infobox_title_typography_typography": "custom", "eael_infobox_title_typography_font_family": "Inter Tight", "eael_infobox_title_typography_font_weight": "600", "eael_infobox_title_typography_font_size": {"unit": "px", "size": 24, "sizes": []}, "eael_infobox_title_typography_line_height": {"unit": "%", "size": 129.99999523162842, "sizes": []}, "eael_infobox_title_typography_text_transform": "none", "eael_infobox_title_typography_letter_spacing": {"unit": "PERCENT", "size": 0, "sizes": []}, "eael_infobox_title_color": "#091439", "eael_infobox_title_margin": {"unit": "px", "top": "0", "right": "0", "bottom": "16", "left": "0", "isLinked": false}, "eael_infobox_content_typography_hover_typography": "custom", "eael_infobox_content_typography_hover_font_family": "Inter Tight", "eael_infobox_content_typography_hover_font_weight": "400", "eael_infobox_content_typography_hover_font_size": {"unit": "px", "size": 18, "sizes": []}, "eael_infobox_content_typography_hover_line_height": {"unit": "%", "size": 160.0000023841858, "sizes": []}, "eael_infobox_content_typography_hover_text_transform": "none", "eael_infobox_content_typography_hover_letter_spacing": {"unit": "PERCENT", "size": 0, "sizes": []}, "eael_infobox_content_color": "#727686", "eael_infobox_content_margin": {"unit": "px", "top": "0", "right": "0", "bottom": "16", "left": "0", "isLinked": false}, "eael_infobox_text": "<p>Awarded for excellence in strategy and execution.</p>", "typography_typography": "custom", "background_background": "classic", "background_color": "#fffaf4", "height": "min-height", "custom_height": {"unit": "px", "size": 227, "sizes": []}}, "elements": [], "isInner": false, "elType": "widget", "widgetType": "eael-info-box"}, {"id": "429:2184", "settings": {"eael_show_infobox_clickable": "", "eael_infobox_img_or_icon": "img", "eael_infobox_content_alignment": "left", "eael_infobox_img_type": "img-on-left", "icon_vertical_position": "top", "eael_infobox_image_resizer": {"unit": "px", "size": 56, "sizes": []}, "eael_infobox_image": {"url": "https://api.figma.com/v1/images/be2e2bdcebbdf83bb8629eb3fc839802846547a7", "id": "", "size": "", "alt": "", "source": "figma"}, "eael_infobox_img_margin": {"unit": "px", "top": "0", "right": "32", "bottom": "0", "left": "0", "isLinked": false}, "eael_section_infobox_container_bg": "#fffaf4", "eael_section_infobox_container_padding": {"unit": "px", "top": "46", "right": "36", "bottom": "46", "left": "36", "isLinked": false}, "eael_infobox_title": "Global Leader Award", "eael_infobox_title_typography_typography": "custom", "eael_infobox_title_typography_font_family": "Inter Tight", "eael_infobox_title_typography_font_weight": "600", "eael_infobox_title_typography_font_size": {"unit": "px", "size": 24, "sizes": []}, "eael_infobox_title_typography_line_height": {"unit": "%", "size": 129.99999523162842, "sizes": []}, "eael_infobox_title_typography_text_transform": "none", "eael_infobox_title_typography_letter_spacing": {"unit": "PERCENT", "size": 0, "sizes": []}, "eael_infobox_title_color": "#091439", "eael_infobox_title_margin": {"unit": "px", "top": "0", "right": "0", "bottom": "16", "left": "0", "isLinked": false}, "eael_infobox_content_typography_hover_typography": "custom", "eael_infobox_content_typography_hover_font_family": "Inter Tight", "eael_infobox_content_typography_hover_font_weight": "400", "eael_infobox_content_typography_hover_font_size": {"unit": "px", "size": 18, "sizes": []}, "eael_infobox_content_typography_hover_line_height": {"unit": "%", "size": 160.0000023841858, "sizes": []}, "eael_infobox_content_typography_hover_text_transform": "none", "eael_infobox_content_typography_hover_letter_spacing": {"unit": "PERCENT", "size": 0, "sizes": []}, "eael_infobox_content_color": "#727686", "eael_infobox_content_margin": {"unit": "px", "top": "0", "right": "0", "bottom": "16", "left": "0", "isLinked": false}, "eael_infobox_text": "<p>Honored for driving digital growth for businesses across 50+ countries.</p>", "typography_typography": "custom", "background_background": "classic", "background_color": "#fffaf4", "height": "min-height", "custom_height": {"unit": "px", "size": 226, "sizes": []}}, "elements": [], "isInner": false, "elType": "widget", "widgetType": "eael-info-box"}], "isInner": false, "elType": "container"}, {"id": "429:2190", "settings": {"eael_show_infobox_clickable": "", "eael_infobox_img_or_icon": "img", "eael_infobox_content_alignment": "left", "eael_infobox_img_type": "img-on-top", "icon_vertical_position": "top", "eael_infobox_image_resizer": {"unit": "px", "size": 92.194091796875, "sizes": []}, "eael_infobox_image": {"url": "https://api.figma.com/v1/images/4723fbc40c4e589b8edc2763715fd02ce349caa1", "id": "", "size": "", "alt": "", "source": "figma"}, "eael_infobox_img_margin": {"unit": "px", "top": "0", "right": "0", "bottom": "48", "left": "0", "isLinked": false}, "eael_section_infobox_container_bg": "#f4faff", "eael_section_infobox_container_padding": {"unit": "px", "top": "101", "right": "48", "bottom": "101", "left": "48", "isLinked": false}, "eael_infobox_title": "Client Satisfaction Excellence", "eael_infobox_title_typography_typography": "custom", "eael_infobox_title_typography_font_family": "Inter Tight", "eael_infobox_title_typography_font_weight": "600", "eael_infobox_title_typography_font_size": {"unit": "px", "size": 24, "sizes": []}, "eael_infobox_title_typography_line_height": {"unit": "%", "size": 129.99999523162842, "sizes": []}, "eael_infobox_title_typography_text_transform": "none", "eael_infobox_title_typography_letter_spacing": {"unit": "PERCENT", "size": 0, "sizes": []}, "eael_infobox_title_color": "#091439", "eael_infobox_title_margin": {"unit": "px", "top": "0", "right": "0", "bottom": "16", "left": "0", "isLinked": false}, "eael_infobox_content_typography_hover_typography": "custom", "eael_infobox_content_typography_hover_font_family": "Inter Tight", "eael_infobox_content_typography_hover_font_weight": "400", "eael_infobox_content_typography_hover_font_size": {"unit": "px", "size": 18, "sizes": []}, "eael_infobox_content_typography_hover_line_height": {"unit": "%", "size": 160.0000023841858, "sizes": []}, "eael_infobox_content_typography_hover_text_transform": "none", "eael_infobox_content_typography_hover_letter_spacing": {"unit": "PERCENT", "size": 0, "sizes": []}, "eael_infobox_content_color": "#727686", "eael_infobox_content_margin": {"unit": "px", "top": "0", "right": "0", "bottom": "16", "left": "0", "isLinked": false}, "eael_infobox_text": "<p>Received client satisfaction excellence award for delivering unmatched client results and satisfaction.</p>", "typography_typography": "custom", "background_background": "classic", "background_color": "#f4faff", "height": "min-height", "custom_height": {"unit": "px", "size": 476, "sizes": []}}, "elements": [], "isInner": false, "elType": "widget", "widgetType": "eael-info-box"}], "isInner": false, "elType": "container"}], "isInner": false, "elType": "container"}, {"id": "429:2196", "settings": {"eael_show_read_more_button": "", "eael_show_meta": "", "arrows": "", "eael_post_grid_border_radius": {"unit": "px", "top": "16", "right": "16", "bottom": "16", "left": "16", "isLinked": true}, "eael_post_grid_bg_color": "#ffffff", "dots_spacing": {"unit": "px", "size": 16, "sizes": []}, "dots_padding": {"unit": "px", "top": 16, "right": 0, "bottom": 0, "left": 0, "isLinked": true}, "eael_post_grid_title_typography_typography": "custom", "eael_post_grid_title_typography_font_family": "Inter Tight", "eael_post_grid_title_typography_font_weight": "600", "eael_post_grid_title_typography_font_size": {"unit": "px", "size": 24, "sizes": []}, "eael_post_grid_title_typography_line_height": {"unit": "%", "size": 129.99999523162842, "sizes": []}, "eael_post_grid_title_typography_text_transform": "none", "eael_post_grid_title_typography_letter_spacing": {"unit": "PERCENT", "size": 0, "sizes": []}, "eael_post_grid_title_color": "#091439", "eael_post_grid_title_hover_color": "#091439", "margin": {"unit": "px", "top": "0", "right": "0", "bottom": "16", "left": "0", "isLinked": false}, "eael_post_grid_excerpt_typography_typography": "custom", "eael_post_grid_excerpt_typography_font_family": "Inter Tight", "eael_post_grid_excerpt_typography_font_weight": "400", "eael_post_grid_excerpt_typography_font_size": {"unit": "px", "size": 18, "sizes": []}, "eael_post_grid_excerpt_typography_line_height": {"unit": "%", "size": 160.0000023841858, "sizes": []}, "eael_post_grid_excerpt_typography_text_transform": "none", "eael_post_grid_excerpt_typography_letter_spacing": {"unit": "PERCENT", "size": 0, "sizes": []}, "eael_post_grid_excerpt_color": "#727686", "typography_typography": "custom", "background_background": "classic", "background_color": "#f4f4ff", "height": "min-height", "custom_height": {"unit": "px", "size": 778, "sizes": []}}, "elements": [], "isInner": false, "elType": "widget", "widgetType": "eael-post-carousel"}, {"id": "429:2650", "settings": {"flex_direction": "column", "content_width": "boxed", "width": {"unit": "px", "size": 1314, "sizes": []}, "padding": {"unit": "px", "top": "63", "right": "55", "bottom": "63", "left": "55", "isLinked": false}, "background_background": "classic", "background_color": "#ffffff", "border_radius": {"unit": "px", "top": "16", "right": "16", "bottom": "16", "left": "16", "isLinked": true}, "height": "min-height", "custom_height": {"unit": "px", "size": 359, "sizes": []}}, "elements": [{"id": "429:2694", "settings": {"flex_direction": "row", "content_width": "boxed", "width": {"unit": "px", "size": 1204, "sizes": []}, "padding": {"unit": "px", "top": "0", "right": "0", "bottom": "0", "left": "0", "isLinked": true}, "height": "min-height", "custom_height": {"unit": "px", "size": 233, "sizes": []}}, "elements": [{"id": "429:2695", "settings": {"flex_direction": "column", "content_width": "boxed", "width": {"unit": "px", "size": 331.1407470703125, "sizes": []}, "padding": {"unit": "px", "top": "0", "right": "0", "bottom": "0", "left": "0", "isLinked": true}, "height": "min-height", "custom_height": {"unit": "px", "size": 144, "sizes": []}}, "elements": [{"id": "429:2696", "settings": {"flex_direction": "column", "content_width": "boxed", "width": {"unit": "px", "size": 331.1407470703125, "sizes": []}, "padding": {"unit": "px", "top": "0", "right": "0", "bottom": "0", "left": "0", "isLinked": true}}, "elements": [{"id": "429:2697", "settings": {"flex_direction": "row", "content_width": "boxed", "width": {"unit": "px", "size": 149, "sizes": []}, "padding": {"unit": "px", "top": "0", "right": "0", "bottom": "0", "left": "0", "isLinked": true}, "height": "min-height", "custom_height": {"unit": "px", "size": 33, "sizes": []}}, "elements": [{"id": "429:2698", "settings": {"title": "FlexiGency", "header_size": "h2", "t": "flex-start", "typography_typography": "custom", "typography_font_family": "Inter Tight", "typography_font_weight": "600", "typography_font_size": {"unit": "px", "size": 30, "sizes": []}, "typography_line_height": {"unit": "%", "size": 110.00000238418579, "sizes": []}, "title_color": "#091439"}, "elements": [], "isInner": false, "elType": "widget", "widgetType": "heading"}], "isInner": false, "elType": "container"}, {"id": "429:2699", "settings": {"editor": "<p>Empower your Brand & Captivate your Audience With MultiGency right away and Boost your Business.  </p>", "typography_typography": "custom", "typography_font_family": "Inter Tight", "typography_font_weight": "400", "typography_font_size": {"unit": "px", "size": 18, "sizes": []}, "typography_line_height": {"unit": "%", "size": 160.0000023841858, "sizes": []}, "title_color": "#727686", "height": "min-height", "custom_height": {"unit": "px", "size": 87, "sizes": []}}, "elements": [], "isInner": false, "elType": "widget", "widgetType": "text-editor"}], "isInner": false, "elType": "container"}], "isInner": false, "elType": "container"}, {"id": "429:2700", "settings": {"flex_direction": "row", "content_width": "boxed", "width": {"unit": "px", "size": 633, "sizes": []}, "padding": {"unit": "px", "top": "0", "right": "0", "bottom": "0", "left": "0", "isLinked": true}}, "elements": [{"id": "429:2701", "settings": {"flex_direction": "column", "content_width": "boxed", "width": {"unit": "px", "size": 105, "sizes": []}, "padding": {"unit": "px", "top": "0", "right": "0", "bottom": "0", "left": "0", "isLinked": true}}, "elements": [{"id": "429:2702", "settings": {"title": "Company", "header_size": "h2", "t": "flex-start", "typography_typography": "custom", "typography_font_family": "Inter Tight", "typography_font_weight": "600", "typography_font_size": {"unit": "px", "size": 24, "sizes": []}, "typography_line_height": {"unit": "px", "size": 20, "sizes": []}, "title_color": "#091439", "height": "min-height", "custom_height": {"unit": "px", "size": 20, "sizes": []}}, "elements": [], "isInner": false, "elType": "widget", "widgetType": "heading"}, {"id": "429:2703", "settings": {"flex_direction": "row", "content_width": "boxed", "width": {"unit": "px", "size": 85, "sizes": []}, "padding": {"unit": "px", "top": "0", "right": "0", "bottom": "0", "left": "0", "isLinked": true}, "background_background": "classic", "background_color": "#ffffff", "height": "min-height", "custom_height": {"unit": "px", "size": 173, "sizes": []}}, "elements": [{"id": "429:2704", "settings": {"eael_advanced_menu_menu": "67", "default_eael_advanced_menu_background": "#ffffff", "default_eael_advanced_menu_item_background_hover": "#ffffff", "default_eael_advanced_menu_item_color": "#878695", "default_eael_advanced_menu_item_indicator_color": "#878695", "default_eael_advanced_menu_dropdown_item_color": "#878695", "default_eael_advanced_menu_dropdown_item_indicator_color": "#878695", "default_eael_advanced_menu_item_typography_typography": "custom", "default_eael_advanced_menu_item_typography_font_family": "Inter Tight", "default_eael_advanced_menu_item_typography_font_weight": "400", "default_eael_advanced_menu_item_typography_font_size": {"unit": "px", "size": 18, "sizes": []}, "default_eael_advanced_menu_item_typography_line_height": {"unit": "%", "size": 160.0000023841858, "sizes": []}, "default_eael_advanced_menu_item_typography_text_transform": "none", "default_eael_advanced_menu_item_typography_letter_spacing": {"unit": "PIXELS", "size": 0, "sizes": []}, "default_eael_advanced_menu_item_typography_line_height_widescreen": {"unit": "%", "size": 160.0000023841858, "sizes": []}, "default_eael_advanced_menu_item_typography_line_height_laptop": {"unit": "%", "size": 160.0000023841858, "sizes": []}, "default_eael_advanced_menu_item_typography_line_height_tablet_extra": {"unit": "%", "size": 160.0000023841858, "sizes": []}, "default_eael_advanced_menu_item_typography_line_height_tablet": {"unit": "%", "size": 160.0000023841858, "sizes": []}, "default_eael_advanced_menu_layout": "vertical", "typography_typography": "custom", "background_background": "classic", "background_color": "#ffffff"}, "elements": [], "isInner": false, "elType": "widget", "widgetType": "eael-advanced-menu"}], "isInner": false, "elType": "container"}], "isInner": false, "elType": "container"}, {"id": "429:2715", "settings": {"flex_direction": "column", "content_width": "boxed", "width": {"unit": "px", "size": 88, "sizes": []}, "padding": {"unit": "px", "top": "0", "right": "0", "bottom": "0", "left": "0", "isLinked": true}}, "elements": [{"id": "429:2716", "settings": {"title": "Support", "header_size": "h2", "t": "flex-start", "typography_typography": "custom", "typography_font_family": "Inter Tight", "typography_font_weight": "600", "typography_font_size": {"unit": "px", "size": 24, "sizes": []}, "typography_line_height": {"unit": "px", "size": 20, "sizes": []}, "title_color": "#091439", "height": "min-height", "custom_height": {"unit": "px", "size": 20, "sizes": []}}, "elements": [], "isInner": false, "elType": "widget", "widgetType": "heading"}, {"id": "429:2717", "settings": {"flex_direction": "row", "content_width": "boxed", "width": {"unit": "px", "size": 88, "sizes": []}, "padding": {"unit": "px", "top": "0", "right": "0", "bottom": "0", "left": "0", "isLinked": true}, "background_background": "classic", "background_color": "#ffffff", "height": "min-height", "custom_height": {"unit": "px", "size": 173, "sizes": []}}, "elements": [{"id": "429:2718", "settings": {"eael_advanced_menu_menu": "67", "default_eael_advanced_menu_background": "#ffffff", "default_eael_advanced_menu_item_background_hover": "#ffffff", "default_eael_advanced_menu_item_color": "#878695", "default_eael_advanced_menu_item_indicator_color": "#878695", "default_eael_advanced_menu_dropdown_item_color": "#878695", "default_eael_advanced_menu_dropdown_item_indicator_color": "#878695", "default_eael_advanced_menu_item_typography_typography": "custom", "default_eael_advanced_menu_item_typography_font_family": "Inter Tight", "default_eael_advanced_menu_item_typography_font_weight": "400", "default_eael_advanced_menu_item_typography_font_size": {"unit": "px", "size": 18, "sizes": []}, "default_eael_advanced_menu_item_typography_line_height": {"unit": "%", "size": 160.0000023841858, "sizes": []}, "default_eael_advanced_menu_item_typography_text_transform": "none", "default_eael_advanced_menu_item_typography_letter_spacing": {"unit": "PIXELS", "size": 0, "sizes": []}, "default_eael_advanced_menu_item_typography_line_height_widescreen": {"unit": "%", "size": 160.0000023841858, "sizes": []}, "default_eael_advanced_menu_item_typography_line_height_laptop": {"unit": "%", "size": 160.0000023841858, "sizes": []}, "default_eael_advanced_menu_item_typography_line_height_tablet_extra": {"unit": "%", "size": 160.0000023841858, "sizes": []}, "default_eael_advanced_menu_item_typography_line_height_tablet": {"unit": "%", "size": 160.0000023841858, "sizes": []}, "default_eael_advanced_menu_layout": "vertical", "typography_typography": "custom", "background_background": "classic", "background_color": "#ffffff"}, "elements": [], "isInner": false, "elType": "widget", "widgetType": "eael-advanced-menu"}], "isInner": false, "elType": "container"}], "isInner": false, "elType": "container"}, {"id": "429:2729", "settings": {"flex_direction": "column", "content_width": "boxed", "width": {"unit": "px", "size": 122, "sizes": []}, "padding": {"unit": "px", "top": "0", "right": "0", "bottom": "0", "left": "0", "isLinked": true}, "background_background": "classic", "background_color": "#ffffff"}, "elements": [{"id": "429:2730", "settings": {"title": "Downloads", "header_size": "h2", "t": "flex-start", "typography_typography": "custom", "typography_font_family": "Inter Tight", "typography_font_weight": "600", "typography_font_size": {"unit": "px", "size": 24, "sizes": []}, "typography_line_height": {"unit": "px", "size": 20, "sizes": []}, "title_color": "#091439", "height": "min-height", "custom_height": {"unit": "px", "size": 20, "sizes": []}}, "elements": [], "isInner": false, "elType": "widget", "widgetType": "heading"}, {"id": "429:2731", "settings": {"eael_advanced_menu_menu": "67", "default_eael_advanced_menu_background": "#ffffff", "default_eael_advanced_menu_item_background_hover": "#ffffff", "default_eael_advanced_menu_item_color": "#878695", "default_eael_advanced_menu_item_indicator_color": "#878695", "default_eael_advanced_menu_dropdown_item_color": "#878695", "default_eael_advanced_menu_dropdown_item_indicator_color": "#878695", "default_eael_advanced_menu_item_typography_typography": "custom", "default_eael_advanced_menu_item_typography_font_family": "Inter Tight", "default_eael_advanced_menu_item_typography_font_weight": "400", "default_eael_advanced_menu_item_typography_font_size": {"unit": "px", "size": 18, "sizes": []}, "default_eael_advanced_menu_item_typography_line_height": {"unit": "%", "size": 160.0000023841858, "sizes": []}, "default_eael_advanced_menu_item_typography_text_transform": "none", "default_eael_advanced_menu_item_typography_letter_spacing": {"unit": "PIXELS", "size": 0, "sizes": []}, "default_eael_advanced_menu_item_typography_line_height_widescreen": {"unit": "%", "size": 160.0000023841858, "sizes": []}, "default_eael_advanced_menu_item_typography_line_height_laptop": {"unit": "%", "size": 160.0000023841858, "sizes": []}, "default_eael_advanced_menu_item_typography_line_height_tablet_extra": {"unit": "%", "size": 160.0000023841858, "sizes": []}, "default_eael_advanced_menu_item_typography_line_height_tablet": {"unit": "%", "size": 160.0000023841858, "sizes": []}, "default_eael_advanced_menu_layout": "vertical", "typography_typography": "custom", "background_background": "classic", "background_color": "#ffffff", "height": "min-height", "custom_height": {"unit": "px", "size": 173, "sizes": []}}, "elements": [], "isInner": false, "elType": "widget", "widgetType": "eael-advanced-menu"}], "isInner": false, "elType": "container"}], "isInner": false, "elType": "container"}], "isInner": false, "elType": "container"}], "isInner": false, "elType": "container"}], "isInner": false, "elType": "container"}], "page_settings": [], "version": "0.5", "title": "figma-to-wp", "type": "section"}