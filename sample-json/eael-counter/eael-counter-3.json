{"content": [{"id": "7b786890", "settings": {"flex_direction": "column"}, "elements": [{"id": "6c87276a", "settings": {"eael_icon_type": "icon", "counter_icon_new": {"value": "far fa-bell", "library": "fa-regular"}, "counter_title": "Counter Title", "counter_layout": "layout-6", "counter_icon_color": "#D12424", "counter_icon_size": {"unit": "px", "size": 34, "sizes": []}, "counter_icon_rotation": {"unit": "px", "size": 61, "sizes": []}, "counter_icon_border_radius": {"unit": "px", "top": "0", "right": "0", "bottom": "0", "left": "0", "isLinked": true}, "counter_icon_padding": {"unit": "px", "top": "0", "right": "0", "bottom": "0", "left": "0", "isLinked": true}, "counter_icon_margin": {"unit": "px", "top": "0", "right": "0", "bottom": "0", "left": "0", "isLinked": true}, "_background_background": "classic", "_background_color": "#8B6767", "_border_radius": {"unit": "px", "top": "10", "right": "10", "bottom": "10", "left": "10", "isLinked": true}}, "elements": [], "isInner": false, "widgetType": "eael-counter", "elType": "widget"}], "isInner": false, "elType": "container"}], "page_settings": {"eael_ext_toc_title": "Table of Contents", "scroll_viewport": "#outer-wrap", "scroll_contentScroll": "#wrap", "eael_ext_scroll_to_top": "yes"}, "version": "0.4", "title": "okk", "type": "container"}