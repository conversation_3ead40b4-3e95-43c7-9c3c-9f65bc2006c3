{"content": [{"id": "53b5b183", "settings": {"flex_direction": "column", "background_background": "classic"}, "elements": [{"id": "2a616b7e", "settings": {"eael_infobox_img_type": "img-on-right", "eael_infobox_img_or_icon": "img", "eael_infobox_image": {"url": "http://essential-addons-dev.test/wp-content/uploads/2025/01/figma-to-wp-image1.png", "id": 17824, "size": "", "alt": "", "source": "library"}, "eael_infobox_title": "Responsive Web Design for Mobile Excellence", "infobox_button_text": "View All Case", "eael_infobox_image_resizer": {"unit": "px", "size": 550, "sizes": []}, "eael_infobox_text": "<p>They helped us provide a streamlined user experience with innovative technology, ensuring seamless navigation.</p>", "eael_show_infobox_button": "yes", "eael_infobox_image_icon_bg_color": "#FFFFFF", "eael_infobox_img_margin": {"unit": "px", "top": "0", "right": "0", "bottom": "0", "left": "40", "isLinked": false}, "eael_section_infobox_container_bg": "#F5F1FF", "eael_section_infobox_container_padding": {"unit": "px", "top": "40", "right": "40", "bottom": "40", "left": "40", "isLinked": true}, "eael_infobox_button_typography_typography": "custom", "eael_infobox_button_typography_font_family": "Inter Tight", "eael_infobox_button_typography_font_size": {"unit": "px", "size": 16, "sizes": []}, "eael_infobox_button_typography_font_weight": "500", "eael_creative_button_padding": {"unit": "px", "top": "16", "right": "24", "bottom": "16", "left": "24", "isLinked": false}, "eael_infobox_button_border_radius": {"unit": "px", "size": 4, "sizes": []}, "eael_infobox_button_background_color": "#091439", "eael_infobox_button_border_border": "solid", "eael_infobox_button_border_width": {"unit": "px", "top": "2", "right": "2", "bottom": "2", "left": "2", "isLinked": true}, "eael_infobox_button_border_color": "#091439", "eael_infobox_title_color": "#091439", "eael_infobox_title_typography_typography": "custom", "eael_infobox_title_typography_font_family": "Inter Tight", "eael_infobox_title_typography_font_size": {"unit": "px", "size": 32, "sizes": []}, "eael_infobox_title_typography_font_weight": "600", "eael_infobox_title_margin": {"unit": "px", "top": "0", "right": "0", "bottom": "16", "left": "0", "isLinked": false}, "eael_infobox_content_margin": {"unit": "px", "top": "0", "right": "0", "bottom": "16", "left": "0", "isLinked": false}, "eael_infobox_content_color": "#727686", "eael_infobox_content_typography_hover_typography": "custom", "eael_infobox_content_typography_hover_font_family": "Inter Tight", "eael_infobox_content_typography_hover_font_size": {"unit": "px", "size": 18, "sizes": []}, "eael_infobox_content_typography_hover_font_weight": "400"}, "elements": [], "isInner": false, "widgetType": "eael-info-box", "elType": "widget"}], "isInner": false, "elType": "container"}], "page_settings": [], "version": "0.4", "title": "eael-info-box-success-stories-1", "type": "container"}