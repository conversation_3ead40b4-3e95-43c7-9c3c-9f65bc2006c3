{
    "content": [
      {
        "id": "3e7945ec",
        "settings": {
          "flex_direction": "column"
        },
        "elements": [
          {
            "id": "2123973c",
            "settings": {
              "typography_typography": "custom",
              "background_background": "classic",
              "background_color": "#f4f4ff",
              "border_radius": {
                "unit": "px",
                "top": "16",
                "right": "16",
                "bottom": "16",
                "left": "16",
                "isLinked": true
              },
              "height": "min-height",
              "custom_height": {
                "unit": "px",
                "size": 778,
                "sizes": []
              },
              "excerpt_expanison_indicator": "...",
              "eael_read_more_text": "Read More",
              // "eael_show_read_more_button": "",
              "read_more_button_text": "Read More",
              // "eael_show_meta": "",
              "autoplay": "",
              // "arrows": "",
              // "eael_post_grid_bg_color": "#FFFFFF",
              // "eael_post_grid_border_radius": {
              //   "unit": "px",
              //   "top": "16",
              //   "right": "16",
              //   "bottom": "16",
              //   "left": "16",
              //   "isLinked": true
              // },
              // "eael_thumbnail_border_radius": {
              //   "unit": "px",
              //   "top": "16",
              //   "right": "16",
              //   "bottom": "0",
              //   "left": "0",
              //   "isLinked": false
              // },
              // "eael_post_grid_title_color": "#091439",
              // "eael_post_grid_title_hover_color": "#091439",
              // "eael_post_grid_title_typography_typography": "custom",
              // "eael_post_grid_title_typography_font_family": "Inter Tight",
              // "eael_post_grid_title_typography_font_size": {
              //   "unit": "px",
              //   "size": 24,
              //   "sizes": []
              // },
              // "eael_post_grid_title_typography_font_weight": "600",
              // "eael_post_grid_excerpt_color": "#727686",
              // "eael_post_grid_excerpt_typography_typography": "custom",
              // "eael_post_grid_excerpt_typography_font_family": "Inter Tight",
              // "eael_post_grid_excerpt_typography_font_size": {
              //   "unit": "px",
              //   "size": 18,
              //   "sizes": []
              // },
              // "eael_post_grid_excerpt_typography_font_weight": "400",
              // "dots_spacing": {
              //   "unit": "px",
              //   "size": 16,
              //   "sizes": []
              // },
              // "dots_padding": {
              //   "unit": "px",
              //   "top": "10",
              //   "right": 0,
              //   "bottom": "10",
              //   "left": 0,
              //   "isLinked": true
              // }
            },
            "elements": [],
            "isInner": false,
            "widgetType": "eael-post-carousel",
            "elType": "widget"
          }
        ],
        "isInner": false,
        "elType": "container"
      }
    ],
    "page_settings": {
      "eael_ext_toc_title": "Table of Contents",
      "scroll_viewport": "#outer-wrap",
      "scroll_contentScroll": "#wrap",
      "eael_ext_scroll_to_top": "yes"
    },
    "version": "0.4",
    "title": "post-carousel",
    "type": "container"
  }