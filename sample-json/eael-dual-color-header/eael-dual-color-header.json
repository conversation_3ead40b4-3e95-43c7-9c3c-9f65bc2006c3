{"content": [{"id": "36e542f0", "settings": {"flex_direction": "row", "width": {"unit": "px", "size": 1318, "sizes": []}, "flex_justify_content": "space-between", "padding": {"unit": "px", "top": "0", "right": "0", "bottom": "0", "left": "0", "isLinked": true}, "height": "min-height", "custom_height": {"unit": "px", "size": 73, "sizes": []}}, "elements": [{"id": "42bb51c2", "settings": {"eael_show_dch_icon_content": "", "eael_dch_first_title": "Dual Heading", "eael_dch_last_title": "", "eael_dch_subtext": "", "eael_dch_container_padding": {"unit": "px", "top": "0", "right": "0", "bottom": "0", "left": "0", "isLinked": true}, "eael_dch_container_margin": {"unit": "px", "top": "0", "right": "0", "bottom": "0", "left": "0", "isLinked": true}, "eael_dch_base_title_color": "#000000", "eael_dch_dual_title_color": "#000000", "eael_dch_first_title_typography_typography": "custom", "eael_dch_first_title_typography_font_family": "Inter Tight", "eael_dch_first_title_typography_font_size": {"unit": "px", "size": 56, "sizes": []}, "eael_dch_first_title_typography_font_weight": "500", "eael_dch_first_title_typography_text_transform": "none", "eael_dch_first_title_typography_line_height": {"unit": "em", "size": 1, "sizes": []}, "eael_dch_first_title_typography_line_height_widescreen": {"unit": "custom", "size": "", "sizes": []}, "eael_dch_first_title_typography_line_height_laptop": {"unit": "custom", "size": "", "sizes": []}, "eael_dch_first_title_typography_line_height_tablet_extra": {"unit": "custom", "size": "", "sizes": []}, "eael_dch_first_title_typography_line_height_tablet": {"unit": "custom", "size": "", "sizes": []}, "eael_dch_first_title_typography_line_height_mobile_extra": {"unit": "custom", "size": "", "sizes": []}, "eael_dch_first_title_typography_line_height_mobile": {"unit": "custom", "size": "", "sizes": []}, "eael_dch_first_title_typography_letter_spacing": {"unit": "px", "size": 0, "sizes": []}, "eael_dch_first_title_typography_word_spacing": {"unit": "px", "size": 0, "sizes": []}}, "elements": [], "isInner": false, "widgetType": "eael-dual-color-header", "elType": "widget"}], "isInner": false, "elType": "container"}], "page_settings": {"eael_ext_toc_title": "Table of Contents", "scroll_viewport": "#outer-wrap", "scroll_contentScroll": "#wrap", "eael_ext_scroll_to_top": "yes"}, "version": "0.4", "title": "dual-color-heading", "type": "container"}