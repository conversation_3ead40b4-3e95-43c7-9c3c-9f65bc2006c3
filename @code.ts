// Constants
const PLUGIN_VERSION = "0.5";
const PLUGIN_TITLE = "figma-to-wp";

let ImageArray: { [nodeId: string]: string } = {}

// Utility functions
class ColorUtils {
  static rgbToHex(color: RGB): string {
    if (!color) return '';

    const toHex = (value: number): string =>
      Math.round(value * 255).toString(16).padStart(2, '0');

    return `#${toHex(color.r)}${toHex(color.g)}${toHex(color.b)}`;
  }
}

class Utils {
  public static processTitleNode(titleNode: any, data: any, titleKey?: string, widgetNodeType?: string, args?: any[]) {
    if (!args || !Array.isArray(args)) {
      args = [];
    }

    if (titleNode) {
      let textNode = titleNode.children?.find(child => child.type === "TEXT") as SceneNode & { characters?: string };

      if (!textNode) {
        textNode = titleNode.type === "TEXT" ? titleNode : textNode;
      }

      if (textNode && !Array.isArray(data.settings)) {
        if (titleKey) {
          data.settings[titleKey] = WidgetProcessor.getTextWithCase(textNode);
        }

        if (args.length > 0) {
          if (args['titleTagKey'] && args['titleTag']) {
            data.settings[args['titleTagKey']] = args['titleTag'];
          }

          if (args['showTitleKey'] && args['showTitle']) {
            data.settings[args['showTitleKey']] = args['showTitle'];
          }
        }

        if (widgetNodeType) {
          WidgetProcessor.processTypographySettings(textNode, data, widgetNodeType);
          WidgetProcessor.processTextColor(textNode, data, widgetNodeType);
          WidgetProcessor.processMargin(titleNode, data, widgetNodeType);
        }
      }
    }
  }

  public static processIconNode(iconNode: any, data: any, iconKey?: string, rotationKey?: string) {
    if (iconNode) {
      let iconName = iconNode.name.substring(5);
      // Extract rotation if exists
      const rotationMatch = iconName.match(/-rotation(-?\d+)$/);
      if (rotationMatch) {
        if (rotationKey) {
          data.settings[rotationKey] = {
            unit: "deg",
            size: parseInt(rotationMatch[1], 10),
            sizes: []
          };
        }
        // Remove the "-rotationXXX" part from the icon name
        iconName = iconName.replace(/-rotation(-?\d+)$/, "");
      }

      if (!Array.isArray(data.settings)) {
        if (iconKey) {
          data.settings[iconKey] = {
            value: `fas ${iconName}`,
            library: "fa-solid",
            // library: 'svg',
            // value: 'http://essential-addons-dev.test/wp-content/uploads/2025/02/icon-fa-long-arrow-alt-right.svg'
          };
        }
      }
    }
  }

  public static processButtonNode(buttonNode: any, data: any, buttonKey?: string, widgetNodeType?: string, args?: any[]) {
    if (buttonNode) {
      if (!Array.isArray(data.settings)) {
        const textNodes = WidgetProcessor.findTextNodes(buttonNode);
        let buttonText: any = [];

        if (textNodes && textNodes.length > 0) {
          textNodes.forEach((textNode) => {
            if ("characters" in textNode) {
              buttonText.push(`${textNode.characters}`);
            }
          });

          if (buttonKey) {
            data.settings[buttonKey] = buttonText.join(" "); // Combine all text nodes into one string
          }

          // example: data.settings.eael_show_infobox_button = "yes";
          if (Array.isArray(args) && args.length > 0) {
            if (args['showButtonKey'] && args['showButton']) {
              data.settings[args['showButtonKey']] = args['showButton'];
            }
          }

          if (widgetNodeType) {
            WidgetProcessor.processTypographySettings(textNodes[0], data, widgetNodeType);
            WidgetProcessor.processTextColor(textNodes[0], data, widgetNodeType);
            WidgetProcessor.processBackground(buttonNode, data, widgetNodeType);
            WidgetProcessor.processPadding(buttonNode, data, widgetNodeType);
            WidgetProcessor.processBorderRadius(buttonNode, data, widgetNodeType);
            WidgetProcessor.processBorderWidth(buttonNode, data, widgetNodeType);
          }
        }
      }
    }
  }

  // 
}

class ElementTypeResolver {
  static getElementType(node: SceneNode): string {
    const typeMap: Record<string, string> = {
      'FRAME': 'section',
      'GROUP': 'column',
      'TEXT': 'widget'
    };

    let type = typeMap[node.type] || 'widget';

    if (node.type === 'FRAME' && node.name.toLowerCase().startsWith('container')) {
      type = 'container';
      return type;
    }

    if (node.type === 'FRAME' && node.name.toLowerCase().startsWith('widget-')) {
      type = 'widget';
    }

    const specialNames = ['section', 'inner-section', 'column', 'inner-column',
      'inner-column-6', 'inner-column-4', 'inner-column-3'];

    if (specialNames.includes(node.name)) {
      type = node.name.includes('column') ? 'column' : 'section';
    }

    let isParentTypeContainer = WidgetProcessor.findParentNodeRecursively(node, undefined, 'container');

    if (isParentTypeContainer) {
      type = type == 'section' ? 'container' : type;
    }

    return type;
  }
}

// EL Widget 1
class ButtonWidgetProcessor {
  public static process(node: any, data: any) {
    let textNode;
    let iconNode;

    if ("children" in node && Array.isArray(node.children)) {
      textNode = node.children?.find(child => child.type === "TEXT");
      iconNode = node.children?.find(child => child.name.startsWith('icon-'));
    }

    Utils.processTitleNode(textNode, data, 'text', 'button-text');

    Utils.processIconNode(iconNode, data, 'selected_icon');

    data.settings.icon_align =
      "children" in node &&
        node.children &&
        node.children.length >= 2 &&
        node.children[0].type === "TEXT" &&
        node.children[1].name.startsWith("icon-")
        ? "row-reverse"
        : "row";

    // data.settings._element_width = 'inherit';
    // find parent and check alignment
    const buttonParentNode = node.parent;
    let buttonParentAlignmentItems;
    let buttonParentAlignment = "";
    if (buttonParentNode && "layoutMode" in buttonParentNode) {
      let buttonParentLayoutMode = buttonParentNode.layoutMode;
      if (buttonParentLayoutMode === "VERTICAL" &&
        "counterAxisAlignItems" in buttonParentNode) {
        buttonParentAlignmentItems = buttonParentNode.counterAxisAlignItems;
      }
      // else if (buttonParentLayoutMode === 'HORIZONTAL' && 'primaryAxisAlignItems' in buttonParentNode) {
      //   buttonParentAlignmentItems = buttonParentNode.primaryAxisAlignItems;
      // }
      else if (buttonParentLayoutMode === "HORIZONTAL" &&
        "counterAxisAlignItems" in buttonParentNode) {
        buttonParentAlignmentItems = buttonParentNode.counterAxisAlignItems;
      }
    }

    buttonParentAlignment =
      buttonParentAlignmentItems === "MAX"
        ? "flex-end"
        : buttonParentAlignmentItems === "CENTER"
          ? "center"
          : buttonParentAlignmentItems === "MIN"
            ? "flex-start"
            : buttonParentAlignment;

    // Vertical alignment
    if (buttonParentAlignment) {
      data.settings._flex_align_self = buttonParentAlignment; // flex-end;
    }
  }
}
// EL Widget 2
class HeadingWidgetProcessor {
  public static process(node: any, data: any) {
    if (!Array.isArray(data.settings)) {
      data.settings.title = node.characters;
      data.settings.header_size = "h2";
      // data.settings._element_width = 'inherit';
      // find parent and check alignment
      const headingParentNode = node.parent;
      let headingParentAlignmentItems;
      let headingParentAlignment = "";
      if (headingParentNode && "layoutMode" in headingParentNode) {
        let headingParentLayoutMode = headingParentNode.layoutMode;
        if (headingParentLayoutMode === "VERTICAL" &&
          "counterAxisAlignItems" in headingParentNode) {
          headingParentAlignmentItems = headingParentNode.counterAxisAlignItems;
        }
        // else if (headingParentLayoutMode === 'HORIZONTAL' && 'primaryAxisAlignItems' in headingParentNode) {
        //   headingParentAlignmentItems = headingParentNode.primaryAxisAlignItems;
        // }
        else if (headingParentLayoutMode === "HORIZONTAL" &&
          "counterAxisAlignItems" in headingParentNode) {
          headingParentAlignmentItems = headingParentNode.counterAxisAlignItems;
        }
      }
      headingParentAlignment =
        headingParentAlignmentItems === "MAX"
          ? "flex-end"
          : headingParentAlignmentItems === "CENTER"
            ? "center"
            : headingParentAlignmentItems === "MIN"
              ? "flex-start"
              : headingParentAlignment;
      // Vertical alignment
      if (headingParentAlignment) {
        data.settings._flex_align_self = headingParentAlignment; // flex-end;
      }
    }
  }
}
// EL Widget 3
class TextEditorWidgetProcessor {
  public static process(node: any, data: any) {
    if (!Array.isArray(data.settings)) {
      data.settings.editor = `<p>${node.characters}</p>`;
    }
  }
}

// EL Widget 4
class ProcessImageWidget {
  public static process(node: any, data: any) {

    if (ImageArray[node.id]) {
      data.settings = {
        ...data.settings,
        image: {
          url: ImageArray[node.id]
        }
      };
    }
  }
}

// EA Widget 1
class CreativeButtonWidgetProcessor {
  public static process(node: any, data: any) {
    let textNode;
    let iconNode;
    if ("children" in node && Array.isArray(node.children)) {
      textNode = node.children?.find(child => child.type === "TEXT");
      iconNode = node.children?.find(child => child.name.startsWith('icon-'));
    }

    Utils.processTitleNode(textNode, data, 'creative_button_text', 'eael-creative-button-text');

    Utils.processIconNode(iconNode, data, 'eael_creative_button_icon_new');

    if (!Array.isArray(data.settings)) {
      data.settings.eael_creative_button_icon_alignment =
        "children" in node &&
          node.children &&
          iconNode &&
          textNode &&
          node.children.length >= 2 &&
          node.children[0].type === "TEXT" &&
          node.children[1].name.startsWith("icon-")
          ? "right"
          : "left";
      if (iconNode && textNode) {
        // get indent from node gap
        const buttonGap = "itemSpacing" in node ? node.itemSpacing : "";
        if (buttonGap) {
          data.settings.eael_creative_button_icon_indent = {
            unit: "px",
            size: buttonGap,
            sizes: [],
          };
        }
        // icon size from icon node width
        const iconSize = iconNode.width;
        data.settings.eael_creative_button_icon_size = {
          unit: "px",
          size: iconSize,
          sizes: [],
        };
      }
    }
    WidgetProcessor.processBackground(node, data, "eael-creative-button-container");
    WidgetProcessor.processBorderRadius(node, data, "eael-creative-button-container");
  }
}
// EA Widget 2
// name: eael-dual-color-header
class DualColorHeadingWidgetProcessor {
  public static process(node: any, data: any) {
    if (!Array.isArray(data.settings)) {
      data.settings.eael_dch_first_title = node.characters;
      data.settings.eael_show_dch_icon_content = ""; // static - no icon
      data.settings.eael_dch_last_title = ""; // static - no last title
      data.settings.eael_dch_subtext = ""; // static - no subtext
      // assuming single text node is the dual color heading widget
      if (node.type === "TEXT") {
        WidgetProcessor.processTypographySettings(node, data, "eael-dch-first-title");
        WidgetProcessor.processTextColor(node, data, "eael-dch-first-title");
      }
      WidgetProcessor.processPadding(node, data, "eael-dch-container");
      WidgetProcessor.processMargin(node, data, "eael-dch-container");
    }
  }
}
// EA Widget 3
class AdvancedMenuWidgetProcessor {
  public static process(node: any, data: any) {
    const menuItemNode = WidgetProcessor.findNodeRecursively(node, undefined, "menu-item");
    const menuItemActiveNode = WidgetProcessor.findNodeRecursively(node, undefined, "active-menu-item");
    const containerNode = WidgetProcessor.findNodeRecursively(node, undefined, "widget-eael-advanced-menu");
    // #ToDo Top Level Item => Item Padding
    if (containerNode) {
      WidgetProcessor.processBackground(containerNode, data, "eael-advanced-menu-container");
    }
    if (menuItemNode) {
      const menuItemTextNode = (menuItemNode as DefaultFrameMixin).children?.find(child => child.type === "TEXT") as SceneNode & { characters?: string };

      WidgetProcessor.processTextColor(menuItemTextNode, data, "eael-advanced-menu-item");
      WidgetProcessor.processTypographySettings(menuItemTextNode, data, "eael-advanced-menu-item");
    }
    if (menuItemActiveNode) {
      const menuItemActiveTextNode = (menuItemActiveNode as DefaultFrameMixin).children?.find(child => child.type === "TEXT") as SceneNode & { characters?: string };

      WidgetProcessor.processTextColor(menuItemActiveTextNode, data, "eael-advanced-menu-item-hover");
      WidgetProcessor.processBackground(menuItemActiveTextNode, data, "eael-advanced-menu-item-hover"); // bg color of main node; not text node;
    }
    if (!Array.isArray(data.settings)) {
      // not empty array.
      data.settings.default_eael_advanced_menu_layout =
        "layoutMode" in node ? node.layoutMode.toLowerCase() : "horizontal"; // default
      WidgetProcessor.processBackground(node, data, "eael-advanced-menu-container");
    }
  }
}
// EA Widget 4
class CtaBoxWidgetProcessor {
  public static process(node: any, data: any) {
    const containerNode = node.name.startsWith("widget") ? node : ""; // widget-eael-cta-box
    const titleNode = WidgetProcessor.findNodeRecursively(node, undefined, "title");
    const contentNode = WidgetProcessor.findNodeRecursively(node, ["TEXT", "FRAME"], "content");
    const buttonNode = WidgetProcessor.findNodeRecursively(node, undefined, "button");
    if (containerNode) {
      WidgetProcessor.processBackground(containerNode, data, "eael-cta-box-container");
      WidgetProcessor.processPadding(containerNode, data, "eael-cta-box-container");
      if ("parent" in containerNode) {
        // #ToDo need to check if parent has 2 childs. otherwise margin should not be applied.
        WidgetProcessor.processMargin(containerNode.parent, data, "eael-cta-box-container");
      }
    }

    Utils.processTitleNode(titleNode, data, 'eael_cta_title', 'eael-cta-box-title');

    if (contentNode && "children" in contentNode) {
      let textItems = [];
      contentNode.children.forEach((frame) => {
        const frameNode = frame; // Ensure it's treated as a SceneNode
        if (frameNode.type === "FRAME") {
          // Find all TEXT nodes, including in inner children
          const textNodes = WidgetProcessor.findTextNodes(frameNode);
          if (textNodes.length > 0) {
            textNodes.forEach((textNode) => {
              if ("characters" in textNode) {
                textItems.push(`<li>${textNode.characters}</li>`);
              }
            });
            WidgetProcessor.processTypographySettings(textNodes[0], data, "eael-cta-box-content");
            WidgetProcessor.processTextColor(textNodes[0], data, "eael-cta-box-content");
          }
        }
      });
      // Generate the final HTML list
      if (!Array.isArray(data.settings)) {
        data.settings.eael_cta_content = `<ul style="list-style: none;">${textItems.join("")}</ul>`;
      }
    }
    else {
      if (!Array.isArray(data.settings)) {
        data.settings.eael_cta_content = "";
      }
    }
    // Currently we are fetching text from all inner child => text node texts. We may restrict to first occurance.
    if (buttonNode) {
      Utils.processButtonNode(buttonNode, data, 'eael_cta_btn_text', 'eael-cta-box-button');

      const buttonSecondaryNode = WidgetProcessor.findNodeRecursively(node, undefined, "button-secondary");

      // let args: any = [];
      // args['showButtonKey'] = 'eael_cta_secondary_btn_is_show';
      // args['showButton'] = 'yes';

      // Utils.processButtonNode(buttonSecondaryNode, data, 'eael_cta_secondary_btn_text', 'eael-cta-box-button-secondary', args);

      // button-secondary: try to replace below code with Utils.processButtonNode
      if (buttonSecondaryNode) {
        const textSecondaryNode = (buttonSecondaryNode as DefaultFrameMixin).children?.find(child => child.type === "TEXT") as SceneNode & { characters?: string };

        data.settings.eael_cat_secondary_btn_normal_border_border = "none"; // static for now;
        data.settings.eael_cta_secondary_btn_is_show = "yes";
        data.settings.eael_cta_secondary_btn_text =
          "characters" in textSecondaryNode
            ? textSecondaryNode.characters
            : "";
        WidgetProcessor.processTypographySettings(textSecondaryNode, data, "eael-cta-box-button-secondary");
        WidgetProcessor.processTextColor(textSecondaryNode, data, "eael-cta-box-button-secondary");
        WidgetProcessor.processBackground(buttonSecondaryNode, data, "eael-cta-box-button-secondary");
      }
      // button-secondary: try to replace above code with Utils.processButtonNode
    }
  }
}
// EA Widget 5
class InfoBoxWidgetProcessor {
  public static process(node: any, data: any) {
    const containerNode = node.name.startsWith("widget") ? node : ""; // widget-eael-info-box
    const titleNode = WidgetProcessor.findNodeRecursively(node, undefined, "title");
    const subtitleNode = WidgetProcessor.findNodeRecursively(node, undefined, "subtitle");
    const contentNode = WidgetProcessor.findNodeRecursively(node, undefined, "content");
    const buttonNode = WidgetProcessor.findNodeRecursively(node, undefined, "button");
    const mainContentNode = WidgetProcessor.findNodeRecursively(node, undefined, "main-content");
    const imageNode = WidgetProcessor.findNodeRecursively(node, undefined, "image");
    const iconNode = WidgetProcessor.findNodeRecursively(node, undefined, "icon");
    data.settings.eael_show_infobox_clickable = ""; // default for now
    if (mainContentNode) {
      // process margin of children and inner children
      WidgetProcessor.processMargin(mainContentNode, data, "eael-info-box-main-content");
    }
    if (imageNode) {
      data.settings.eael_infobox_img_or_icon = "img";
      const imageParentNode = WidgetProcessor.findParentNode(node, imageNode) as DefaultFrameMixin;
      const imageParentNodeChildren = imageParentNode?.children;
      const imageIndex = imageParentNodeChildren === null || imageParentNodeChildren === void 0 ? void 0 : imageParentNodeChildren.indexOf(imageNode);
      const mainContentNodeIndex = imageParentNodeChildren === null || imageParentNodeChildren === void 0 ? void 0 : imageParentNodeChildren.indexOf(mainContentNode);
      let imagePosition = "";
      if (imageIndex >= 0 && mainContentNodeIndex >= 0) {
        const flexDirection = (imageParentNode === null || imageParentNode === void 0 ? void 0 : imageParentNode.layoutMode) === "HORIZONTAL" ? "row" : "column";
        imagePosition =
          imageIndex < mainContentNodeIndex
            ? flexDirection === "row"
              ? "left"
              : "top"
            : flexDirection === "row"
              ? "right"
              : "bottom";
        let mainContentAlignment = "left";
        if (mainContentNode && "layoutMode" in mainContentNode) {
          let mainContentLayoutMode = mainContentNode.layoutMode;
          let mainContentAlignmentItems;
          if (mainContentLayoutMode === "VERTICAL" &&
            "counterAxisAlignItems" in mainContentNode) {
            mainContentAlignmentItems = mainContentNode.counterAxisAlignItems;
          }
          else if (mainContentLayoutMode === "HORIZONTAL" &&
            "primaryAxisAlignItems" in mainContentNode) {
            mainContentAlignmentItems = mainContentNode.primaryAxisAlignItems;
          }
          mainContentAlignment =
            mainContentAlignmentItems === "MAX"
              ? "right"
              : mainContentAlignmentItems === "CENTER"
                ? "center"
                : mainContentAlignment;
        }
        let imageParentVerticalAlignment = "middle";
        if (imageParentNode && "layoutMode" in imageParentNode) {
          let imageParentLayoutMode = imageParentNode.layoutMode;
          let imageParentAlignmentItems;
          if (imageParentLayoutMode === "VERTICAL" &&
            "counterAxisAlignItems" in imageParentNode) {
            imageParentAlignmentItems = imageParentNode.counterAxisAlignItems;
          }
          else if (imageParentLayoutMode === "HORIZONTAL" &&
            "primaryAxisAlignItems" in imageParentNode) {
            imageParentAlignmentItems = imageParentNode.primaryAxisAlignItems;
          }
          imageParentVerticalAlignment =
            imageParentAlignmentItems === "MAX"
              ? "bottom"
              : imageParentAlignmentItems === "CENTER"
                ? "middle"
                : imageParentAlignmentItems === "MIN"
                  ? "top"
                  : imageParentVerticalAlignment;
        }
        if (imagePosition === "right") {
          data.settings.eael_infobox_content_alignment = "left";
          data.settings.eael_infobox_img_type = "img-on-right";
        }
        else if (imagePosition === "left") {
          data.settings.eael_infobox_content_alignment = mainContentAlignment;
          data.settings.eael_infobox_img_type = "img-on-left";
        }
        else if (imagePosition === "top") {
          data.settings.eael_infobox_content_alignment = mainContentAlignment; // content left, center, right
          data.settings.eael_infobox_img_type = "img-on-top";
        }
        else if (imagePosition === "bottom") {
          data.settings.eael_infobox_content_alignment = mainContentAlignment;
          data.settings.eael_infobox_img_type = "img-on-bottom";
        }
        data.settings.icon_vertical_position = imageParentVerticalAlignment;
      }
      // image alignment from constraints
      if ("constraints" in imageNode) {
        const constraints = imageNode.constraints;
        if (constraints.vertical === "MIN") {
          data.settings.icon_vertical_position = "top";
        }
        else if (constraints.vertical === "MAX") {
          data.settings.icon_vertical_position = "bottom";
        }
        else if (constraints.vertical === "CENTER") {
          data.settings.icon_vertical_position = "middle";
        }
      }
      // image size
      const imageSize = imageNode.width;
      data.settings.eael_infobox_image_resizer = {
        unit: "px",
        size: imageSize,
        sizes: [],
      };
      // image url
      // "eael_infobox_image": {
      //         "url": "http://essential-addons-dev.test/wp-content/uploads/2025/01/figma-to-wp-image1.png",
      //         "id": 17824,
      //         "size": "",
      //         "alt": "",
      //         "source": "library"
      //       },
      // need to check if node has background image. then fetch the image url
      // Check if the image node has fills
      if ('fills' in imageNode && Array.isArray(imageNode.fills) && imageNode.fills.length > 0) {
        const imageFill = imageNode.fills.find((fill) => fill.type === "IMAGE");


        if (imageFill && ImageArray[imageNode.id]) {
          const imageHash = imageFill.imageHash;
          data.settings.eael_infobox_image = {
            url: ImageArray[imageNode.id],
            size: '',
            alt: '',
            source: 'figma' // not working. we will revamp later
          };
        }
      }
      WidgetProcessor.processBorderRadius(imageNode, data, "eael-info-box-image");
      if (imagePosition !== "right") {
        WidgetProcessor.processMargin(imageNode, data, "eael-info-box-image");
      }
    }
    if (iconNode && !imageNode) {
      // if content node is sibling of icon node, then it is icon instead of image. we dont consider icon from main-content frame
      const iconParentNode = WidgetProcessor.findParentNode(node, iconNode) as DefaultFrameMixin;
      const iconParentNodeChildren = iconParentNode?.children;
      const mainContentNodeIndex = iconParentNodeChildren === null || iconParentNodeChildren === void 0 ? void 0 : iconParentNodeChildren.indexOf(mainContentNode);
      if (mainContentNodeIndex >= 0) {
        data.settings.eael_infobox_img_or_icon = "icon";
      }
    }
    if (containerNode) {
      WidgetProcessor.processBackground(containerNode, data, "eael-info-box-container");
      WidgetProcessor.processPadding(containerNode, data, "eael-info-box-container");
    }

    Utils.processTitleNode(titleNode, data, "eael_infobox_title", "eael-info-box-title");

    // Subtitle
    let args: any = [];
    args['titleTagKey'] = 'eael_infobox_sub_title_tag';
    args['titleTag'] = 'h4';

    args['showTitleKey'] = 'eael_infobox_show_sub_title';
    args['showTitle'] = 'yes';

    Utils.processTitleNode(subtitleNode, data, "eael_infobox_sub_title", "eael-info-box-subtitle", args);

    if (contentNode &&
      ("children" in contentNode || contentNode.type === "TEXT")) {
      let textItems = [];
      if (contentNode.type === "TEXT") {
        if ("characters" in contentNode) {
          textItems.push(`<p>${contentNode.characters}</p>`);
        }
        WidgetProcessor.processTypographySettings(contentNode, data, "eael-info-box-content");
        WidgetProcessor.processTextColor(contentNode, data, "eael-info-box-content");
        WidgetProcessor.processMargin(contentNode, data, "eael-info-box-content");
      }
      else {
        contentNode.children.forEach((frame) => {
          const frameNode = frame; // Ensure it's treated as a SceneNode
          if (frameNode.type === "FRAME") {
            // Find all TEXT nodes, including in inner children
            const textNodes = WidgetProcessor.findTextNodes(frameNode);
            if (textNodes.length > 0) {
              textNodes.forEach((textNode) => {
                if ("characters" in textNode) {
                  textItems.push(`<p>${textNode.characters}</p>`);
                }
              });
              WidgetProcessor.processTypographySettings(textNodes[0], data, "eael-info-box-content");
              WidgetProcessor.processTextColor(textNodes[0], data, "eael-info-box-content");
            }
          }
        });
      }
      // Generate the final HTML list
      if (!Array.isArray(data.settings)) {
        data.settings.eael_infobox_text = `${textItems.join("")}`;
      }
    }
    // Currently we are fetching text from all inner child => text node texts. We may restrict to first occurance.
    if (buttonNode) {
      if (!Array.isArray(data.settings)) {
        // let args: any = [];
        // args['showButtonKey'] = 'eael_show_infobox_button';
        // args['showButton'] = 'yes';

        // Utils.processButtonNode(buttonNode, data, 'infobox_button_text', 'eael-info-box-button', args);

        // Try using Utils.processButtonNode instead of below code
        // Ensure data.settings is an object, not an array
        const textNodes = WidgetProcessor.findTextNodes(buttonNode);
        let buttonText = []; // unnecessary array; it can be string;
        if (textNodes && textNodes.length > 0) {
          textNodes.forEach((textNode) => {
            if ("characters" in textNode) {
              buttonText.push(`${textNode.characters}`);
            }
          });
          data.settings.infobox_button_text = buttonText.join(" "); // Combine all text nodes into one string
          data.settings.eael_show_infobox_button = "yes";
          WidgetProcessor.processTypographySettings(textNodes[0], data, "eael-info-box-button");
          WidgetProcessor.processTextColor(textNodes[0], data, "eael-info-box-button");
          WidgetProcessor.processBackground(buttonNode, data, "eael-info-box-button");
          WidgetProcessor.processPadding(buttonNode, data, "eael-info-box-button");
          WidgetProcessor.processBorderRadius(buttonNode, data, "eael-info-box-button");
          WidgetProcessor.processBorderWidth(buttonNode, data, "eael-info-box-button");
          // WidgetProcessor.processMargin(buttonNode, data, 'eael-info-box-button');
        }
        // Try using Utils.processButtonNode instead of above code

        // get icon node within button starts with icon-
        const iconNode = (buttonNode as DefaultFrameMixin).children?.find(child => child.name.startsWith('icon-'));

        if (iconNode) {
          Utils.processIconNode(iconNode, data, 'eael_infobox_button_icon_new', 'eael_infobox_button_icon_rotate');

          // icon size
          const iconSize = iconNode.width;
          data.settings.eael_infobox_button_icon_size = {
            unit: "px",
            size: iconSize,
            sizes: [],
          };
          // icon indent
          const buttonGap = "itemSpacing" in buttonNode ? buttonNode.itemSpacing : "";
          if (buttonGap) {
            data.settings.eael_infobox_button_icon_indent = {
              unit: "px",
              size: buttonGap,
              sizes: [],
            };
          }
          // icon alignment
          // button node has two child. text and icon. if text is first child then icon on the right
          const iconIndex = (buttonNode as DefaultFrameMixin).children?.indexOf(iconNode);
          const textIndex = (buttonNode as DefaultFrameMixin).children?.indexOf(textNodes[0]);

          if (iconIndex >= 0 && textIndex >= 0) {
            data.settings.eael_infobox_button_icon_alignment =
              iconIndex > textIndex ? "right" : "left";
          }
        }
      }
    }
  }
}
// EA Widget 6
class PostCarouselWidgetProcessor {
  public static process(node: any, data: any) {
    const titleNode = WidgetProcessor.findNodeRecursively(node, undefined, "title");
    const contentNode = WidgetProcessor.findNodeRecursively(node, undefined, "content");
    // const mainContentNode = WidgetProcessor.findNodeRecursively(node, undefined, "main-content");
    const postNode = WidgetProcessor.findNodeRecursively(node, undefined, "post");
    const imageNode = WidgetProcessor.findNodeRecursively(node, undefined, "image");
    const controlsNode = WidgetProcessor.findNodeRecursively(node, undefined, "controls");
    // if ( mainContentNode ) {
    //   // process margin of children and inner children
    //   WidgetProcessor.processMargin(mainContentNode, data, 'eael-info-box-main-content');
    // }
    data.settings.eael_show_read_more_button = ""; // static for now; no read more button;
    data.settings.eael_show_meta = ""; // static for now;
    data.settings.arrows = ""; // static for now;
    if (postNode) {
      WidgetProcessor.processBorderRadius(postNode, data, "eael-post-carousel-post");
      WidgetProcessor.processBackground(postNode, data, "eael-post-carousel-post");
    }
    if (controlsNode) {
      const dotNode = WidgetProcessor.findNodeRecursively(node, undefined, "dot");
      const activeDotNode = WidgetProcessor.findNodeRecursively(node, undefined, "active-dot");
      if (dotNode) {
        // const dotParentNode = dotNode.parent as SceneNode; // Optional to use SceneNode. It helps typecasting only;
        const dotParentNode = dotNode.parent;
        const dotGap = "itemSpacing" in controlsNode ? controlsNode.itemSpacing : "";
        const dotParentGap = "itemSpacing" in dotParentNode ? dotParentNode.itemSpacing : "";
        if (dotGap) {
          data.settings.dots_spacing = {
            unit: "px",
            size: dotGap,
            sizes: [],
          };
        }
        if (dotParentGap) {
          data.settings.dots_padding = {
            unit: "px",
            top: dotParentGap,
            right: 0,
            bottom: 0,
            left: 0,
            isLinked: true,
          };
        }
      }
    }

    Utils.processTitleNode(titleNode, data, '', 'eael-post-carousel-title');

    if (contentNode &&
      ("children" in contentNode || contentNode.type === "TEXT")) {
      if (contentNode.type === "TEXT") {
        WidgetProcessor.processTypographySettings(contentNode, data, "eael-post-carousel-content");
        WidgetProcessor.processTextColor(contentNode, data, "eael-post-carousel-content");
      }
      else {
        contentNode.children.forEach((frame) => {
          const frameNode = frame; // Ensure it's treated as a SceneNode
          if (frameNode.type === "FRAME") {
            // Find all TEXT nodes, including in inner children
            const textNodes = WidgetProcessor.findTextNodes(frameNode);
            if (textNodes.length > 0) {
              WidgetProcessor.processTypographySettings(textNodes[0], data, "eael-post-carousel-content");
              WidgetProcessor.processTextColor(textNodes[0], data, "eael-post-carousel-content");
            }
          }
        });
      }
    }
    if (imageNode) {
      WidgetProcessor.processBorderRadius(imageNode, data, "eael-post-carousel-image");
    }
  }
}
// EA Widget 7
class TestimonialWidgetProcessor {
  public static process(node: any, data: any) {
    const containerNode = node.name.startsWith("widget") ? node : ""; // widget-eael-testimonial
    const nameNode = WidgetProcessor.findNodeRecursively(node, undefined, "name");
    const companyNode = WidgetProcessor.findNodeRecursively(node, undefined, "company");
    const imageNode = WidgetProcessor.findNodeRecursively(node, undefined, "image");
    const contentNode = WidgetProcessor.findNodeRecursively(node, undefined, "content");
    const reviewNode = WidgetProcessor.findNodeRecursively(node, undefined, "review");
    data.settings.eael_testimonial_quotation_color = ""; // static for now;
    let quotationPosition = {
      unit: "%",
      size: "",
      sizes: [],
    };
    data.settings.eael_testimonial_quotation_top = quotationPosition; // static for now;
    data.settings.eael_testimonial_quotation_right = quotationPosition; // static for now;
    data.settings.eael_testimonial_style = "content-bottom-icon-title-inline"; // static for now; selected preset;
    data.settings.eael_testimonial_user_display_block = "yes"; // static for now; name and company in two lines
    // "image": {
    //   "url": "http://essential-addons-dev.test/wp-content/uploads/2025/03/image1.png",
    //   "id": 20131,
    //   "size": "",
    //   "alt": "",
    //   "source": "library"
    // },
    if (containerNode) {
      WidgetProcessor.processBackground(containerNode, data, "eael-testimonial-container");
    }

    Utils.processTitleNode(nameNode, data, 'eael_testimonial_name', 'eael-testimonial-name');
    Utils.processTitleNode(companyNode, data, 'eael_testimonial_company_title', 'eael-testimonial-company');
    Utils.processTitleNode(contentNode, data, 'eael_testimonial_description', 'eael-testimonial-content');

    if (imageNode) {
      let imageWidth = imageNode.width;
      data.settings.eael_testimonial_image_width = {
        unit: "px",
        size: imageWidth,
        sizes: [],
      };
      data.settings.eael_testimonial_max_image_width = {
        unit: "%",
        size: imageWidth,
        sizes: [],
      };
      data.settings.eael_testimonial_image_rounded =
        "testimonial-avatar-rounded"; // static for now; #todo if has corner radius then show as rounded or add border radius.
      WidgetProcessor.processMargin(imageNode, data, "eael-testimonial-image");
    }
    if (reviewNode) {
      let ratingNode = (reviewNode as DefaultFrameMixin).children?.find(child => child.name.startsWith('star')) as SceneNode;

      let ratingwidth = "width" in ratingNode ? ratingNode.width : "";
      if (ratingwidth) {
        data.settings.eael_testimonial_rating_item_size = {
          unit: "px",
          size: ratingwidth,
          sizes: [],
        };
      }
      // get parent and itemspacing
      if (reviewNode.parent) {
        WidgetProcessor.processMargin(reviewNode.parent, data, "eael-testimonial-review");
      }
      if (ratingNode.parent) {
        WidgetProcessor.processMargin(ratingNode.parent, data, "eael-testimonial-star");
      }
      WidgetProcessor.processTextColor(ratingNode, data, "eael-testimonial-review");
    }
  }
}
// EA Widget 8
class FeatureListWidgetProcessor {
  public static process(node: any, data: any) {
    const containerNode = node.name.startsWith("widget") ? node : ""; // widget-eael-testimonial
    let listNode;
    let titleNode;
    let iconNode;
    let contentNode;
    // Find all nodes named "list" within the widget
    const listNodes = WidgetProcessor.findAllNodes(node, "list");
    if (!Array.isArray(data.settings)) {
      data.settings.eael_feature_list_connector = "";
      data.settings.eael_feature_list = [];
      if (listNodes.length > 0) {
        listNodes.forEach((listNode, index) => {
          // listNode already available
          titleNode = WidgetProcessor.findNodeRecursively(listNode, undefined, "title");
          iconNode = WidgetProcessor.findNodeRecursively(listNode, undefined, "icon");
          contentNode = WidgetProcessor.findNodeRecursively(listNode, undefined, "content");
          const featureItem: FeatureListItem = {
            eael_feature_list_title: titleNode
              ? WidgetProcessor.getTextWithCase(titleNode)
              : "",
            eael_feature_list_content: contentNode
              ? WidgetProcessor.getTextWithCase(contentNode)
              : "",
          };
          // If an icon exists, add icon data
          if (iconNode) {
            let iconName = iconNode.name.substring(5);
            // Extract rotation if exists
            const rotationMatch = iconName.match(/-rotation(-?\d+)$/);
            if (rotationMatch) {
              data.settings.eael_infobox_button_icon_rotate = {
                unit: "deg",
                size: parseInt(rotationMatch[1], 10),
                sizes: [],
              };
              // Remove the "-rotationXXX" part from the icon name
              iconName = iconName.replace(/-rotation(-?\d+)$/, "");
            }
            // Set cleaned icon name
            featureItem.eael_feature_list_icon_new = {
              value: `fas ${iconName}`,
              library: "fa-solid",
            };
          }
          data.settings.eael_feature_list.push(featureItem);
        });
      }
    }
    // if ( containerNode ) {
    //   WidgetProcessor.processBackground(containerNode, data, 'eael-feature-list-container');
    // }
    if (listNode) {
      if (!Array.isArray(data.settings)) {
      }
      if ("parent" in listNode) {
        WidgetProcessor.processMargin(listNode.parent, data, "eael-feature-list-list");
      }
    }

    let args: any = [];
    args['titleTagKey'] = 'eael_feature_list_title_size';
    args['titleTag'] = 'p';

    Utils.processTitleNode(titleNode, data, '', 'eael-feature-list-title', args);

    if (contentNode) {
      let textNode = contentNode.children?.find(child => child.type === "TEXT") as SceneNode & { characters?: string };

      if (!textNode) {
        textNode = contentNode.type === "TEXT" ? contentNode : textNode;
      }
      // array check to fix type related errors
      if (textNode && !Array.isArray(data.settings)) {
        // not empty array.
        let textNodeCharacter = "characters" in textNode ? textNode.characters : "";
        // if (textNodeCharacter?.startsWith('content')) {
        //   textNodeCharacter = textNodeCharacter.replace(/^content\s*[\|:-]?\s*/, '');
        // }
        // data.settings.eael_testimonial_description = textNodeCharacter;
        // WidgetProcessor.processTypographySettings(textNode, data, 'eael-feature-list-content');
        // WidgetProcessor.processTextColor(textNode, data, 'eael-feature-list-content');
        // WidgetProcessor.processMargin(textNode, data, 'eael-feature-list-content');
      }
    }

    if (iconNode) {
      let iconWidth = iconNode.width;
      if (!Array.isArray(data.settings)) {
        data.settings.eael_feature_list_icon_color = ""; // static for now
        data.settings.eael_feature_list_icon_size = {
          unit: "px",
          size: iconWidth,
          sizes: [],
        };
        data.settings.eael_feature_list_icon_circle_size = {
          unit: "px",
          size: iconWidth,
          sizes: [],
        };
      }
      if ("parent" in iconNode) {
        WidgetProcessor.processMargin(iconNode.parent, data, "eael-feature-list-icon");
      }
      WidgetProcessor.processPadding(iconNode, data, "eael-feature-list-icon");
      WidgetProcessor.processBackground(iconNode, data, "eael-feature-list-icon");
    }
  }
}
// EA Widget 9
class CounterWidgetProcessor {
  public static process(node: any, data: any) {
    const containerNode = node.name.startsWith("widget") ? node : "";
    const numberNode = WidgetProcessor.findNodeRecursively(node, undefined, "number");
    const titleNode = WidgetProcessor.findNodeRecursively(node, undefined, "title");
    const iconNode = WidgetProcessor.findNodeRecursively(node, undefined, "icon");
    if (containerNode) {
      WidgetProcessor.processBorderRadius(containerNode, data, "eael-counter-container");
      WidgetProcessor.processBackground(containerNode, data, "eael-counter-container");
    }
    if (numberNode) {
      let textNode = (numberNode as DefaultFrameMixin).children?.find(child => child.type === "TEXT") as SceneNode & { characters?: string };

      if (!textNode) {
        textNode = numberNode.type === "TEXT" ? numberNode : textNode;
      }
      // array check to fix type related errors
      if (textNode && !Array.isArray(data.settings)) {
        let ending_number = WidgetProcessor.getTextWithCase(textNode);
        let ending_number_raw = ending_number;
        // take number only from the text. it may contain plus icon
        data.settings.ending_number = ending_number.replace(/\D/g, "");
        // take non number as suffix or prefix
        data.settings.number_suffix = ending_number_raw.replace(/\d/g, ""); // suffix for now;
        WidgetProcessor.processTypographySettings(textNode, data, "eael-counter-number");
        WidgetProcessor.processTypographySettings(textNode, data, "eael-counter-suffix"); // we could use same for suffix but code gets longer and complex. but for color, we use 1 method for both number and suffix.
        WidgetProcessor.processTextColor(textNode, data, "eael-counter-number"); // using same for suffix
      }
    }

    Utils.processTitleNode(titleNode, data, 'counter_title', 'eael-counter-title');

    if (iconNode) {
      Utils.processIconNode(iconNode, data, 'counter_icon_new', 'counter_icon_rotation');

      let iconWidth = iconNode.width;
      if (!Array.isArray(data.settings)) {
        data.settings.counter_icon_color = ""; // static for now
        data.settings.counter_layout = "layout-6"; // static for now
        data.settings.eael_icon_type = "icon"; // static for now
        data.settings.counter_icon_size = {
          unit: "px",
          size: iconWidth,
          sizes: [],
        };
      }
      if ("parent" in iconNode) {
        WidgetProcessor.processMargin(iconNode.parent, data, "eael-counter-icon");
      }
      WidgetProcessor.processPadding(iconNode, data, "eael-counter-icon");
      WidgetProcessor.processBorderRadius(iconNode, data, "eael-counter-icon");
    }
  }
}
// EA Widget 10
class LogoCarouselWidgetProcessor {
  public static process(node: any, data: any) {
    const containerNode = node.name.startsWith("widget") ? node : "";
    const imageNode = WidgetProcessor.findNodeRecursively(node, undefined, "image");
    if (containerNode) {
      // WidgetProcessor.processBorderRadius(containerNode, data, 'eael-logo-carousel-container');
      // WidgetProcessor.processBackground(containerNode, data, 'eael-logo-carousel-container');
    }
  }
}
// EA Widget 11
class WooProductListWidgetProcessor {
  public static process(node: any, data: any) {
    const containerNode = node.name.startsWith("widget") ? node : "";
    const titleNode = WidgetProcessor.findNodeRecursively(node, undefined, "title");
    const contentNode = WidgetProcessor.findNodeRecursively(node, undefined, "content");
    const regularPriceNode = WidgetProcessor.findNodeRecursively(node, undefined, "regular-price");
    const salePriceNode = WidgetProcessor.findNodeRecursively(node, undefined, "sale-price");
    const buttonAddToCartNode = WidgetProcessor.findNodeRecursively(node, undefined, "button-add-to-cart");
    const buttonViewProductNode = WidgetProcessor.findNodeRecursively(node, undefined, "button-view-product");
    const categoryNameNode = WidgetProcessor.findNodeRecursively(node, undefined, "category-name");
    const categoryIconNode = WidgetProcessor.findNodeRecursively(node, undefined, "category-icon");
    const reviewNode = WidgetProcessor.findNodeRecursively(node, undefined, "review");
    const imageNode = WidgetProcessor.findNodeRecursively(node, undefined, "image");
    const totalSoldNode = WidgetProcessor.findNodeRecursively(node, undefined, "total-sold");
    if (!Array.isArray(data.settings)) {
      // Static for now
      data.settings.eael_product_list_content_header_badge_sale_text = "Sale";
      data.settings.eael_product_list_content_header_badge_stock_out_text =
        "Stock Out";
      data.settings.eael_product_list_content_body_excerpt_expanison_indicator =
        "...";
      data.settings.eael_product_list_content_footer_total_sold_text =
        "Total Sold:";
      data.settings.eael_product_list_content_footer_total_sold_remaining_text =
        "Remaining:";
      data.settings.eael_product_list_content_footer_add_to_cart_simple_text =
        "Buy Now";
      data.settings.eael_product_list_content_footer_add_to_cart_variable_text =
        "Select options";
      data.settings.eael_product_list_content_footer_add_to_cart_grouped_text =
        "View products";
      data.settings.eael_product_list_content_footer_add_to_cart_external_text =
        "Buy Now";
      data.settings.eael_product_list_content_footer_add_to_cart_default_text =
        "Read More";
      data.settings.eael_product_list_content_footer_quick_view_text =
        "View Product";
      data.settings.eael_product_list_content_footer_not_found_text =
        "No products found!";
      data.settings.show_load_more_text = "Load More";
      if (reviewNode) {
        //
      }
      else {
        data.settings.eael_woo_product_list_rating_show = "";
      }
      if (categoryNameNode) {
        //
      }
      else {
        data.settings.eael_woo_product_list_category_show = "";
      }
      if (totalSoldNode) {
        data.settings.eael_woo_product_list_total_sold_show = "yes";
      }
    }
    if (containerNode) {
      // WidgetProcessor.processBorderRadius(containerNode, data, 'eael-logo-carousel-container');
      WidgetProcessor.processBackground(containerNode, data, "eael-woo-product-list-container");
      if ("parent" in containerNode) {
        WidgetProcessor.processMargin(containerNode.parent, data, "eael-woo-product-list-container");
      }
      WidgetProcessor.processPadding(containerNode, data, "eael-woo-product-list-container");
    }
  }
}

class WidgetProcessor {
  // Main Method
  public static processWidgetSettings(node: SceneNode & {
    characters?: string,
    fills?: readonly Paint[],
    type?: string
  }, baseData: ElementData): void {

    if (!baseData.widgetType) return;

    const processors: Record<string, (node: any, data: ElementData) => void> = {
      'button': ButtonWidgetProcessor.process,
      'heading': HeadingWidgetProcessor.process,
      'text-editor': TextEditorWidgetProcessor.process,
      'image': ProcessImageWidget.process,
      'eael-creative-button': CreativeButtonWidgetProcessor.process,
      'eael-dual-color-header': DualColorHeadingWidgetProcessor.process,
      'eael-advanced-menu': AdvancedMenuWidgetProcessor.process,
      'eael-cta-box': CtaBoxWidgetProcessor.process,
      'eael-info-box': InfoBoxWidgetProcessor.process,
      'eael-post-carousel': PostCarouselWidgetProcessor.process,
      'eael-testimonial': TestimonialWidgetProcessor.process,
      'eael-feature-list': FeatureListWidgetProcessor.process,
      'eael-counter': CounterWidgetProcessor.process,
      'eael-logo-carousel': LogoCarouselWidgetProcessor.process,
      'eael-woo-product-list': WooProductListWidgetProcessor.process,
    };

    const processor = processors[baseData.widgetType];
    if (processor) {
      processor(node, baseData);
    }

    if (baseData.widgetType !== 'eael-cta-box') {
      this.processTypographySettings(node, baseData);
      this.processTextColor(node, baseData);
    }
  }

  // process methods
  public static processTypographySettings(node: any, data: ElementData, widgetNodeType?: string): void {
    if (!Array.isArray(data.settings)) {
      const typography = {
        typography: 'custom',
        fontFamily: node.fontName?.family,
        fontWeight: node.fontName?.style === "Regular" ? "400"
          : node.fontName?.style === "Medium" ? "500"
            : node.fontName?.style === "SemiBold" ? "600"
              : node.fontName?.style === "Bold" ? "700"
                : node.fontName?.style === "ExtraBold" ? "800"
                  : node.fontName?.style === "Black" ? "900"
                    : node.fontName?.style,
        fontSize: node.fontSize ? { unit: "px", size: node.fontSize, sizes: [] } : undefined,
        lineHeight: node.lineHeight ? { unit: node.lineHeight.unit === "PERCENT" ? "%" : "px", size: node.lineHeight.value, sizes: [] } : undefined,
        textTransform: node.textCase?.toLowerCase() || 'none',
        letterSpacing: node.letterSpacing ? { unit: node.letterSpacing.unit, size: node.letterSpacing.value, sizes: [] } : undefined,
      };

      // if original then, change to none;
      typography.textTransform = (node.textCase === "ORIGINAL" || node.textCase === 'TITLE') ? 'none' : typography.textTransform;

      const widgetMap: Record<string, Record<string, string>> = {
        'eael-creative-button': {
          'eael-creative-button-text': 'eael_creative_button_typography',
        },
        'eael-dual-color-header': {
          'eael-dch-first-title': 'eael_dch_first_title_typography',
        },
        'eael-advanced-menu': {
          'eael-advanced-menu-item': 'default_eael_advanced_menu_item_typography',
        },
        'eael-cta-box': {
          'eael-cta-box-title': 'eael_cta_title_typography',
          'eael-cta-box-content': 'eael_cta_content_typography',
          'eael-cta-box-button': 'eael_cta_btn_typography',
          'eael-cta-box-button-secondary': 'eael_cta_secondary_btn_typography',
        },
        'eael-info-box': {
          'eael-info-box-title': 'eael_infobox_title_typography',
          'eael-info-box-subtitle': 'eael_infobox_sub_title_typography',
          'eael-info-box-content': 'eael_infobox_content_typography_hover',
          'eael-info-box-button': 'eael_infobox_button_typography',
        },
        'eael-post-carousel': {
          'eael-post-carousel-title': 'eael_post_grid_title_typography',
          'eael-post-carousel-content': 'eael_post_grid_excerpt_typography',
        },
        'eael-testimonial': {
          'eael-testimonial-name': 'eael_testimonial_name_typography',
          'eael-testimonial-company': 'eael_testimonial_position_typography',
          'eael-testimonial-content': 'eael_testimonial_description_typography',
        },
        'eael-feature-list': {
          'eael-feature-list-title': 'eael_feature_list_title_typography',
        },
        'eael-counter': {
          'eael-counter-number': 'counter_num_typography',
          'eael-counter-title': 'counter_title_typography',
          'eael-counter-suffix': 'section_number_suffix_typography',
        },
      };

      if (data.widgetType && widgetNodeType && widgetMap[data.widgetType]?.[widgetNodeType]) {
        const prefix = widgetMap[data.widgetType][widgetNodeType];
        data.settings[`${prefix}_typography`] = typography.typography;
        data.settings[`${prefix}_font_family`] = typography.fontFamily;
        data.settings[`${prefix}_font_weight`] = typography.fontWeight;

        if (typography.fontSize) data.settings[`${prefix}_font_size`] = typography.fontSize;
        if (typography.lineHeight) data.settings[`${prefix}_line_height`] = typography.lineHeight;
        if (typography.textTransform) data.settings[`${prefix}_text_transform`] = typography.textTransform;
        if (typography.letterSpacing) data.settings[`${prefix}_letter_spacing`] = typography.letterSpacing;

        if (data.widgetType === 'eael-info-box' && widgetNodeType === 'eael-info-box-button') {
          // data.settings[`${prefix}_text_decoration`] = 'underline'; // Static for now
        }

        if (widgetNodeType === 'eael-advanced-menu-item') {
          data.settings[`${prefix}_line_height_widescreen`] = typography.lineHeight;
          data.settings[`${prefix}_line_height_laptop`] = typography.lineHeight;
          data.settings[`${prefix}_line_height_tablet_extra`] = typography.lineHeight;
          data.settings[`${prefix}_line_height_tablet`] = typography.lineHeight;
          // data.settings[`${prefix}_line_height_mobile_extra`] = typography.lineHeight; // may be figma design line height is not perfect for mobile line height?
          // data.settings[`${prefix}_line_height_mobile`] = typography.lineHeight;
        }
      } else {
        data.settings.typography_typography = typography.typography;
        data.settings.typography_font_family = typography.fontFamily;
        data.settings.typography_font_weight = typography.fontWeight;
        if (typography.fontSize) data.settings.typography_font_size = typography.fontSize;
        if (typography.lineHeight) data.settings.typography_line_height = typography.lineHeight;
      }
      //
    }
  }

  public static processTextColor(node: SceneNode, data: ElementData, widgetNodeType?: string): void {
    if (node.type !== "TEXT" || !("fills" in node) || !Array.isArray(node.fills) || node.fills.length === 0) {
      return;
    }

    const fill = node.fills[0];
    if (!["SOLID", "GRADIENT_LINEAR"].includes(fill.type) || Array.isArray(data.settings)) {
      return;
    }

    const titleColor = ColorUtils.rgbToHex(fill.color);
    const widgetType = data.widgetType;

    const widgetSettingsMap: Record<string, Record<string, string>> = {
      "button": {
        "button-text": "button_text_color",
      },
      "eael-creative-button": {
        "eael-creative-button-text": "eael_creative_button_text_color",
      },
      "eael-dual-color-header": { // name: eael-dual-color-header (not *-heading)
        "eael-dch-first-title": "eael_dch_base_title_color",
      },
      "eael-advanced-menu": {
        "eael-advanced-menu-item": "default_eael_advanced_menu_item_color",
        "eael-advanced-menu-item-hover": "default_eael_advanced_menu_item_color_hover"
      },
      "eael-cta-box": {
        "eael-cta-box-title": "eael_cta_title_color",
        "eael-cta-box-content": "eael_cta_content_color",
        "eael-cta-box-button": "eael_cta_btn_normal_text_color",
        "eael-cta-box-button-secondary": "eael_cta_secondary_btn_normal_text_color",
      },
      "eael-info-box": {
        "eael-info-box-title": "eael_infobox_title_color",
        "eael-info-box-subtitle": "eael_infobox_sub_title_color",
        "eael-info-box-content": "eael_infobox_content_color",
        "eael-info-box-button": "eael_infobox_button_text_color"
      },
      "eael-post-carousel": {
        "eael-post-carousel-title": "eael_post_grid_title_color", // name is correct
        "eael-post-carousel-content": "eael_post_grid_excerpt_color",
      },
      "eael-testimonial": {
        'eael-testimonial-name': 'eael_testimonial_name_color',
        'eael-testimonial-company': 'eael_testimonial_company_color',
        'eael-testimonial-content': 'eael_testimonial_description_color',
        "eael-testimonial-review": "eael_testimonial_rating_item_color",
      },
      "eael-feature-list": {
        'eael-feature-list-title': 'eael_feature_list_title_color',
      },
      "eael-counter": {
        'eael-counter-title': 'counter_title_color',
        'eael-counter-number': 'counter_num_color',
        // 'eael-counter-suffix': 'section_number_suffix_color', // same as number for now
      },

    };

    if (widgetType && widgetNodeType && widgetSettingsMap[widgetType]?.[widgetNodeType]) {
      data.settings[widgetSettingsMap[widgetType][widgetNodeType]] = titleColor;
      if (widgetType === "eael-cta-box" && widgetNodeType === "eael-cta-box-button") {
        data.settings.eael_cta_btn_hover_text_color = ""; // static for now
      }

      if (widgetNodeType === 'eael-cta-box-button-secondary') {
        data.settings.eael_cta_secondary_btn_hover_text_color = titleColor;
      }

      if (widgetType === "eael-info-box" && widgetNodeType === "eael-info-box-button") {
        data.settings.eael_infobox_button_hover_text_color = titleColor;
      }
      if (widgetNodeType === "eael-creative-button-text") {
        data.settings.eael_creative_button_hover_text_color = titleColor;
        data.settings.eael_creative_button_icon_color = titleColor; // icon color for now;
        data.settings.eael_creative_button_hover_icon_color = titleColor;
      }
      if (widgetNodeType === "eael-dch-first-title") {
        data.settings.eael_dch_dual_title_color = titleColor;
      }
      if (widgetNodeType === "eael-advanced-menu-item") {
        data.settings.default_eael_advanced_menu_item_indicator_color = titleColor;
        data.settings.default_eael_advanced_menu_dropdown_item_color = titleColor;
        data.settings.default_eael_advanced_menu_dropdown_item_indicator_color = titleColor;
      }
      if (widgetNodeType === "eael-advanced-menu-item-hover") {
        data.settings.default_eael_advanced_menu_item_indicator_color_hover = titleColor;
        data.settings.default_eael_advanced_menu_dropdown_item_color_hover = titleColor;
        data.settings.default_eael_advanced_menu_dropdown_item_indicator_color_hover = titleColor;
      }

      if (widgetNodeType === "eael-post-carousel-title") {
        data.settings.eael_post_grid_title_hover_color = titleColor;
      }

      if (widgetNodeType === "eael-counter-number") {
        data.settings.section_number_suffix_color = titleColor;
      }
    } else {
      data.settings.title_color = titleColor;
    }
  }

  public static processBackground(node: SceneNode, baseData: ElementData, widgetNodeType?: string): void {
    if (node.type === "TEXT" || !("fills" in node) || !Array.isArray(node.fills) || node.fills.length === 0) {
      if (widgetNodeType === "eael-info-box-button" && !Array.isArray(baseData.settings)) {
        baseData.settings.eael_infobox_button_background_color = "";
        baseData.settings.eael_infobox_button_hover_background_color = "";
      }
      return;
    }

    const fill = node.fills[0];
    if (Array.isArray(baseData.settings)) {
      return;
    }

    let backgroundType = fill.type === "SOLID" ? "classic" : "gradient";
    let backgroundColor = ColorUtils.rgbToHex(fill.color);
    let backgroundColorB = fill.type === "GRADIENT_LINEAR" ? ColorUtils.rgbToHex(fill.gradientStops[1]?.color) : "";

    if (!backgroundColor) {
      backgroundColor = backgroundColorB;
    }

    const widgetSettingsMap: Record<string, Record<string, string>> = {
      "eael-creative-button": {
        "eael-creative-button-container": "eael_creative_button_background_color"
      },
      "eael-advanced-menu": {
        "eael-advanced-menu-container": "default_eael_advanced_menu_background",
        "eael-advanced-menu-item-hover": "default_eael_advanced_menu_item_background_hover"
      },
      "eael-cta-box": {
        "eael-cta-box-container": "eael_cta_bg_color",
        "eael-cta-box-button": "eael_cta_btn_normal_bg_color",
        "eael-cta-box-button-secondary": "eael_cta_secondary_btn_normal_bg_color_color",
        // "default": "eael_cta_bg_color",
      },
      "eael-info-box": {
        "eael-info-box-container": "eael_section_infobox_container_bg",
        "eael-info-box-button": "eael_infobox_button_background_color"
      },
      "eael-post-carousel": {
        "eael-post-carousel-post": "eael_post_grid_bg_color"
      },
      "eael-testimonial": {
        "eael-testimonial-container": "eael_testimonial_background"
      },
      "eael-feature-list": {
        "eael-feature-list-icon": "eael_feature_list_icon_background_color"
      },
      "eael-counter": {
        "eael-counter-container": "_background_color" // advanced tab
      },
      "eael-woo-product-list": {
        "eael-woo-product-list-container": "eael_product_list_container_normal_background_color" // advanced tab
      },
    };

    if (baseData.widgetType && widgetNodeType && widgetSettingsMap[baseData.widgetType]?.[widgetNodeType]) {
      baseData.settings[widgetSettingsMap[baseData.widgetType][widgetNodeType]] = backgroundColor;
      if (baseData.widgetType === "eael-info-box" && widgetNodeType === "eael-info-box-button") {
        baseData.settings.eael_infobox_button_background_color_color = backgroundColor; // why eael_infobox_button_background_color not sure; may be we can replace with eael_infobox_button_background_color_color;
        baseData.settings.eael_infobox_button_hover_background_color_color = backgroundColor; // why eael_infobox_button_background_color not sure; may be we can replace with eael_infobox_button_background_color_color;

        baseData.settings.eael_infobox_button_hover_background_color = backgroundColor;

        if (backgroundColorB) {
          baseData.settings.eael_infobox_button_background_color_color_b = backgroundColorB;
          baseData.settings.eael_infobox_button_hover_background_color_color_b = backgroundColorB;

          // above two lines or bottom two lines. need to remove later.
          baseData.settings.eael_infobox_button_background_color_b = backgroundColorB;
          baseData.settings.eael_infobox_button_hover_background_color_b = backgroundColorB;
        }
      } else if (widgetNodeType === "eael-creative-button-container") {
        baseData.settings.eael_creative_button_hover_background_color = backgroundColor;
      }

      if (widgetNodeType === "eael-cta-box-button") {
        baseData.settings.eael_cta_btn_hover_bg_color = backgroundColor;
      }

      if (widgetNodeType === "eael-cta-box-button-secondary") {
        baseData.settings.eael_cta_secondary_btn_normal_bg_color_background = 'classic';

        baseData.settings.eael_cta_secondary_btn_hover_bg_color_background = 'classic';
        baseData.settings.eael_cta_secondary_btn_hover_bg_color_color = backgroundColor;
      }

      if (widgetNodeType === "eael-advanced-menu-container") {
        baseData.settings.default_eael_advanced_menu_item_background_hover = backgroundColor;
      }

      if (widgetNodeType === "eael-feature-list-icon") {
        baseData.settings.eael_feature_list_icon_background_background = 'classic';
      }

      if (widgetNodeType === "eael-counter-container") {
        baseData.settings._background_background = 'classic';
      }

      if (widgetNodeType === "eael-woo-product-list-container") {
        baseData.settings.eael_product_list_container_normal_background_background = 'classic';
      }
    } else {
      baseData.settings.background_background = backgroundType;
      baseData.settings.background_color = backgroundColor;
      if (backgroundColorB) {
        baseData.settings.background_color_b = backgroundColorB;
      }
    }
  }

  public static processPadding(node: SceneNode, baseData: ElementData, widgetNodeType?: string): void {
    // We need to check if the node has padding properties
    if (!("paddingLeft" in node) && !("paddingRight" in node) &&
      !("paddingTop" in node) && !("paddingBottom" in node)) {
      return;
    }

    if (Array.isArray(baseData.settings)) {
      return;
    }

    // Extract padding values, defaulting to 0 if not present
    const paddingLeft = "paddingLeft" in node ? node.paddingLeft : 0;
    const paddingRight = "paddingRight" in node ? node.paddingRight : 0;
    const paddingTop = "paddingTop" in node ? node.paddingTop : 0;
    const paddingBottom = "paddingBottom" in node ? node.paddingBottom : 0;

    // Check if all paddings are the same
    const isLinked = paddingLeft === paddingRight &&
      paddingRight === paddingTop &&
      paddingTop === paddingBottom;

    const padding = {
      unit: "px",
      top: paddingTop?.toString(),
      right: paddingRight?.toString(),
      bottom: paddingBottom?.toString(),
      left: paddingLeft?.toString(),
      isLinked: isLinked
    };

    // Different widgets use different property names for padding
    const widgetSettingsMap: Record<string, Record<string, string>> = {
      "eael-dual-color-header": {
        "eael-dch-container": "eael_dch_container_padding",
      },
      // "eael-advanced-menu": {
      //   "eael-advanced-menu-container": "default_eael_advanced_menu_padding",
      //   "eael-advanced-menu-item": "default_eael_advanced_menu_item_padding"
      // },
      "eael-cta-box": {
        "eael-cta-box-container": "eael_cta_container_padding",
        // "eael-cta-box-button": "eael_cta_btn_padding"
      },
      "eael-info-box": {
        "eael-info-box-container": "eael_section_infobox_container_padding",
        "eael-info-box-button": "eael_creative_button_padding" // bug in the ea. need to rename this control
      },
      "eael-feature-list": {
        "eael-feature-list-icon": "eael_feature_list_icon_padding",
      },
      "eael-counter": {
        "eael-counter-icon": "counter_icon_padding",
      },
      "eael-woo-product-list": {
        "eael-woo-product-list-container": "eael_product_list_container_padding",
      },
    };

    // Handle different widget types
    if (baseData.widgetType && widgetNodeType && widgetSettingsMap[baseData.widgetType]?.[widgetNodeType]) {
      baseData.settings[widgetSettingsMap[baseData.widgetType][widgetNodeType]] = padding;
    } else {
      // Default padding property for elements without specific widget mapping
      baseData.settings.padding = padding;
    }
  }

  public static processMargin(node: any, data: ElementData, widgetNodeType?: string): void {
    // Check if node has itemSpacing property (auto layout property)
    if (!(node.type == "TEXT" || node.name.startsWith('image')) && !("itemSpacing" in node)) return;

    // Skip if settings is an array
    if (Array.isArray(data.settings)) return;

    let itemSpacing = node.itemSpacing || 0;

    // Create margin object based on layout direction
    // In Figma, item spacing applies between items based on the layout direction
    let isHorizontal = "layoutMode" in node && node.layoutMode === "HORIZONTAL";

    let getParent = node.type === "TEXT" || node.name.startsWith('image') ? 1 : 0; // InfoBox: if image (first) and main-content, then need to fetch parent to get image bottom margin

    if (getParent) {
      const parent = node.parent;
      if (parent || ("itemSpacing" in parent)) {
        itemSpacing = parent.itemSpacing || 0;
        isHorizontal = "layoutMode" in parent && parent.layoutMode === "HORIZONTAL";
      }
    }

    let margin = {
      unit: "px",
      // top: isHorizontal ? "0" : itemSpacing.toString(),
      top: isHorizontal ? "0" : "0",
      right: isHorizontal ? itemSpacing.toString() : "0",
      bottom: isHorizontal ? "0" : itemSpacing.toString(),
      // left: isHorizontal ? itemSpacing.toString() : "0",
      left: isHorizontal ? "0" : "0",
      isLinked: false
    };

    // Map widget types to their margin property names
    const widgetSettingsMap: Record<string, Record<string, string>> = {
      "eael-dual-color-header": {
        "eael-dch-container": "eael_dch_container_margin",
      },
      // "eael-advanced-menu": {
      //   "eael-advanced-menu-container": "default_eael_advanced_menu_margin",
      //   "eael-advanced-menu-item": "default_eael_advanced_menu_item_margin"
      // },
      "eael-cta-box": {
        "eael-cta-box-container": "eael_cta_container_margin",
        "eael-cta-box-title": "eael_cta_title_margin",
        // "eael-cta-box-button": "eael_cta_btn_margin",
        // "default": "eael_cta_margin"
      },
      "eael-info-box": {
        // "eael-info-box-container": "eael_infobox_margin",
        "eael-info-box-title": "eael_infobox_title_margin",
        "eael-info-box-subtitle": "eael_infobox_subtitle_margin",
        "eael-info-box-content": "eael_infobox_content_margin",
        // "eael-info-box-button": "eael_infobox_button_margin",
        "eael-info-box-image": "eael_infobox_img_margin",
        "eael-info-box-main-content": "parent-wrap" // parent-wrap is placeholder value. just to make it not null.
      },
      "eael-testimonial": {
        "eael-testimonial-image": "eael_testimonial_image_margin",
        "eael-testimonial-content": "eael_testimonial_description_margin",
        "eael-testimonial-review": "eael_testimonial_rating_margin",
        "eael-testimonial-star": "eael_testimonial_rating_item_distance",
      },
      "eael-feature-list": {
        "eael-feature-list-icon": "eael_feature_list_icon_space",
        "eael-feature-list-list": "eael_feature_list_space_between",
      },
      "eael-counter": {
        "eael-counter-icon": "counter_icon_margin",
      },
      "eael-woo-product-list": {
        "eael-woo-product-list-container": "counter_icon_margin",
      },
    };

    // Set the margin based on widget type and node type
    if (data.widgetType && widgetNodeType && widgetSettingsMap[data.widgetType]?.[widgetNodeType]) {
      if ('eael-info-box-main-content' == widgetNodeType) {
        // margin will be assigned to direct childrens.
        node.children.forEach((child, index) => {
          const childNode = child as SceneNode;
          if (!Array.isArray(data.settings)) {
            if (childNode.name === "title") {
              data.settings.eael_infobox_title_margin = margin;
            } else if (childNode.name === "subtitle") {
              data.settings.eael_infobox_subtitle_margin = margin;
            } else if (childNode.name === "content") {
              data.settings.eael_infobox_content_margin = margin;
            } else if (childNode.name === "button") {
              data.settings.eael_infobox_button_margin = margin;
            }
          }

        });

      }
      else if ('eael-dch-container' == widgetNodeType || 'eael-cta-box-container' == widgetNodeType) {
        margin.right = '0';
        margin.bottom = '0';

        data.settings[widgetSettingsMap[data.widgetType][widgetNodeType]] = margin;
      }
      else if ('eael-feature-list-icon' === widgetNodeType) {
        data.settings[widgetSettingsMap[data.widgetType][widgetNodeType]] = {
          unit: 'px',
          size: isHorizontal ? margin.right : margin.bottom,
          sizes: []
        };
      }
      else if ('eael-feature-list-list' === widgetNodeType) {
        data.settings[widgetSettingsMap[data.widgetType][widgetNodeType]] = {
          unit: 'px',
          size: isHorizontal ? margin.right : margin.bottom,
          sizes: []
        };

        data.settings.eael_feature_list_title_bottom_space = {
          unit: 'px',
          size: isHorizontal ? margin.right : margin.bottom,
          sizes: []
        };
      }
      // else if (
      //   'eael-info-box-title' === widgetNodeType
      //   || 'eael-info-box-content' === widgetNodeType
      //   || 'eael-info-box-subtitle' === widgetNodeType
      //   || 'eael-info-box-button' === widgetNodeType
      // ) {
      //   data.settings[widgetSettingsMap[data.widgetType][widgetNodeType]] = margin;
      // } 
      else {
        data.settings[widgetSettingsMap[data.widgetType][widgetNodeType]] = margin;
      }
    } else {
      data.settings.margin = margin;
    }
  }

  public static processBorderRadius(node: SceneNode, baseData: ElementData, widgetNodeType?: string): void {
    if (!("cornerRadius" in node) || node.cornerRadius === 0) return;

    const cornerRadius = node.cornerRadius ?? 0;

    if (!Array.isArray(baseData.settings)) {

      let border_radius = {
        unit: "px",
        top: cornerRadius.toString(),
        right: cornerRadius.toString(),
        bottom: cornerRadius.toString(),
        left: cornerRadius.toString(),
        isLinked: true
      };

      let border_radius_v2 = {
        unit: "px",
        size: cornerRadius.toString(),
        sizes: [],
      };

      if (baseData.widgetType && baseData.widgetType === 'eael-cta-box') {

        if (widgetNodeType && widgetNodeType === 'eael-cta-box-button') {
          baseData.settings.eael_cta_btn_border_radius = border_radius_v2;
        } else {
          baseData.settings.eael_cta_border_radius = border_radius_v2;
        }
      } else if (baseData.widgetType && baseData.widgetType === 'eael-info-box') {

        if (widgetNodeType && widgetNodeType === 'eael-info-box-button') {
          baseData.settings.eael_infobox_button_border_radius = border_radius_v2;
        }

        if (widgetNodeType && widgetNodeType === 'eael-info-box-image') {
          baseData.settings.eael_infobox_img_shape = 'radius';
          baseData.settings.eael_infobox_img_shape_radius = border_radius;
        }
      } else if (baseData.widgetType && baseData.widgetType === 'eael-creative-button') {

        if (widgetNodeType && widgetNodeType === 'eael-creative-button-container') {
          baseData.settings.eael_creative_button_border_radius = border_radius_v2;
        }
      } else if (baseData.widgetType && baseData.widgetType === 'eael-post-carousel') {

        if (widgetNodeType && widgetNodeType === 'eael-post-carousel-image') {
          baseData.settings.eael_thumbnail_border_radius = border_radius;
        }

        if (widgetNodeType && widgetNodeType === 'eael-post-carousel-post') {
          baseData.settings.eael_post_grid_border_radius = border_radius;
        }

        if (widgetNodeType && widgetNodeType === 'eael-counter-icon') {
          baseData.settings.counter_icon_border_radius = border_radius;
        }
      } else if (baseData.widgetType && baseData.widgetType === 'eael-counter') {
        if (widgetNodeType && widgetNodeType === 'eael-counter-container') {
          baseData.settings._border_radius = border_radius; // from advanced tab
        }
      } else {
        baseData.settings.border_radius = border_radius;
      }
    }

  }

  public static processBorderWidth(node: SceneNode, data: ElementData, widgetNodeType?: string): void {
    if (!("strokeWeight" in node) || !node.strokeWeight) return;

    if (!Array.isArray(data.settings)) {
      const borderWidth = {
        unit: "px",
        top: node.strokeWeight.toString(),
        right: node.strokeWeight.toString(),
        bottom: node.strokeWeight.toString(),
        left: node.strokeWeight.toString(),
        isLinked: true
      };

      if (data.widgetType && data.widgetType === 'eael-cta-box') {
        if (widgetNodeType && widgetNodeType === 'eael-cta-box-button') {
          data.settings.eael_cat_btn_normal_border_width = borderWidth;
          data.settings.eael_cat_btn_normal_border_border = 'solid'; // Set border style
        } else {
          data.settings.eael_cta_border_width = borderWidth;
          data.settings.eael_cta_border_border = 'solid'; // Set border style for container
        }
      }

      if (data.widgetType && data.widgetType === 'eael-info-box') {
        if (widgetNodeType && widgetNodeType === 'eael-info-box-button') {
          data.settings.eael_infobox_button_border_width = borderWidth;
          data.settings.eael_infobox_button_border_border = 'solid'; // Set border style

          if (node.strokes && node.strokes.length > 0 && 'color' in node.strokes[0]) {
            data.settings.eael_infobox_button_border_color = ColorUtils.rgbToHex(node.strokes[0].color);
          }
        }
      }

    }
  }

  // utility
  public static getParentAlignment(node: any) {
    const parentNode = node.parent;
    let parentAlignmentItems = '';

    if (parentNode && 'layoutMode' in parentNode) {
      let parentLayoutMode = parentNode.layoutMode;

      if (parentLayoutMode === 'VERTICAL' && 'counterAxisAlignItems' in parentNode) {
        parentAlignmentItems = parentNode.counterAxisAlignItems;
      }
      // else if (parentLayoutMode === 'HORIZONTAL' && 'primaryAxisAlignItems' in parentNode) {
      //   parentAlignmentItems = parentNode.primaryAxisAlignItems;
      // }
      else if (parentLayoutMode === 'HORIZONTAL' && 'counterAxisAlignItems' in parentNode) {
        parentAlignmentItems = parentNode.counterAxisAlignItems;
      }
    }

    return parentAlignmentItems;
  }

  public static getTextWithCase(textNode: SceneNode) {
    if (!("characters" in textNode)) return "";

    let text = textNode.characters;
    let caseType = textNode.textCase;

    switch (caseType) {
      case "UPPER":
        text = text.toUpperCase();
        break;
      case "LOWER":
        text = text.toLowerCase();
        break;
      case "TITLE":
        text = text.replace(/\b\w/g, (char) => char.toUpperCase());
        break;
      case "SMALL_CAPS":
      case "SMALL_CAPS_FORCED":
        // Small caps may require custom font handling
        console.warn("Small caps not directly supported in JavaScript");
        break;
      default:
        // Keep original case
        break;
    }

    return text;
  }

  // based on given name. not immediate parent.
  public static findParentNode(rootNode: SceneNode, targetNode: SceneNode): SceneNode | null {
    // Base case: if rootNode is the same as targetNode, return null (no parent)
    if (rootNode === targetNode) {
      return null;
    }

    // Check if rootNode is a container type that can have children
    if ('children' in rootNode) {
      // Check direct children
      for (const child of rootNode.children) {
        if (child === targetNode) {
          return rootNode;
        }
      }

      // Recursively check in children
      for (const child of rootNode.children) {
        const found = WidgetProcessor.findParentNode(child, targetNode);
        if (found) {
          return found;
        }
      }
    }

    // Not found in this branch
    return null;
  }

  // Recursive function to find a node by type and name
  public static findNodeRecursively(node: SceneNode, type: string | string[] | undefined, name: string): SceneNode | undefined {
    if (type === undefined || (Array.isArray(type) ? type.includes(node.type) : node.type === type)) {
      // Check if the current node matches the name criteria
      if (node.name.toLowerCase().startsWith(name)) {
        // console.log(`Found matching node: ${node.name}`); // Debug: Log the found node
        return node;
      }
    }

    // If the node has children, search recursively
    if ("children" in node && Array.isArray(node.children)) {
      for (const child of node.children) {
        const foundNode = this.findNodeRecursively(child, type, name);
        if (foundNode) {
          return foundNode;
        }
      }
    }

    // If no matching node is found, return undefined
    return undefined;
  };

  public static findParentNodeRecursively(node: SceneNode | null, type: string | string[] | undefined, name: string): SceneNode | undefined {
    if (!node || !("parent" in node)) {
      return undefined; // Base case: No parent exists
    }

    const parent = node.parent as SceneNode; // Get the parent node

    // Check if parent matches the criteria
    if (type === undefined || (Array.isArray(type) ? type.includes(parent.type) : parent.type === type)) {
      if (parent.name.toLowerCase().startsWith(name)) {
        return parent;
      }
    }

    // Recursively check the parent's parent
    return this.findParentNodeRecursively(parent, type, name);
  }

  public static findTextNodes(node: SceneNode): SceneNode[] {
    let textNodes: SceneNode[] = [];

    // If the node is a TEXT node, add it to the list
    if (node.type === "TEXT" && node.characters) {
      textNodes.push(node);
    }

    // If the node has children, recursively search them
    if ('children' in node) {
      node?.children.forEach(child => {
        textNodes = textNodes.concat(this.findTextNodes(child)); // Recursively search children
      });
    }

    return textNodes;
  }

  /**
 * Recursively finds all nodes with the given name.
 */
  public static findAllNodes(node: SceneNode, name: string, nodes: SceneNode[] = []): SceneNode[] {
    if (node.name === name) {
      nodes.push(node);
    }

    if ("children" in node) {
      node.children.forEach(child => WidgetProcessor.findAllNodes(child, name, nodes));
    }

    return nodes;
  }
}

// Global Functions

/**
 * ✅ Make processSelection async so it returns a Promise
 */
async function processSelection(selection: readonly SceneNode[]): JsonResult {
  const images = await processImageNodes(selection); // ✅ Wait for images to be processed
  ImageArray = { ...images }

  return {
    content: selection.map((node) => processNode(node)),
    page_settings: [],
    version: PLUGIN_VERSION,
    title: PLUGIN_TITLE,
    type: "section"
  };
}

function generate8CharHash(): string {
  return Math.random().toString(36).substring(2, 10); // 8 chars
}

function processNode(node: SceneNode, parentHeight?: number): ElementData {
  const isInner = node.name.includes("inner");

  const baseData: ElementData = {
    // id: node.id,
    id: generate8CharHash(),
    settings: {},
    elements: [],
    isInner: isInner,
    elType: ElementTypeResolver.getElementType(node)
  };

  // Process container settings if needed
  if (baseData.elType === "container") {
    if (!Array.isArray(baseData.settings)) {
      // get flex_direction from node direction 
      let nodeDirection = 'column';
      let justifyContent = '';

      if ('layoutMode' in node) {
        nodeDirection = node.layoutMode === "HORIZONTAL" ? "row" : "column";

        if (node.layoutMode === 'HORIZONTAL') {
          if ('primaryAxisAlignItems' in node) {
            justifyContent = node.primaryAxisAlignItems == 'SPACE_BETWEEN' ? 'space-between' : (node.primaryAxisAlignItems == 'MAX' ? 'flex-end' : (node.primaryAxisAlignItems == 'CENTER' ? 'center' : justifyContent));
          }
        }

      }
      baseData.settings = {
        flex_direction: nodeDirection,
        content_width: 'boxed', // Default; boxed, full
        width: {
          unit: 'px',
          size: node.width,
          sizes: []
        }
      };

      if (justifyContent) {
        baseData.settings.flex_justify_content = justifyContent;
      }

      WidgetProcessor.processPadding(node, baseData);
    }
  }

  // Process widget type
  if (node.type === "TEXT" || node.name.startsWith('widget-')) {
    baseData.widgetType = node.name.startsWith('widget-')
      ? node.name.substring(7)
      : "heading";

    // it may contain menu id: eael-advanced-menu-298; here menu id is 298
    if (baseData.widgetType.startsWith('eael-advanced-menu') && baseData.widgetType.length > 18) {
      const menuIdMatch = baseData.widgetType.match(/(\d+)$/);
      if (menuIdMatch) {
        if (!Array.isArray(baseData.settings)) {
          baseData.settings.eael_advanced_menu_menu = menuIdMatch[1]; // Extract matched number
        }
      }

      baseData.widgetType = 'eael-advanced-menu';
    }

    WidgetProcessor.processWidgetSettings(node as any, baseData);
  }

  // Process background
  WidgetProcessor.processBackground(node, baseData);

  // Process border radius
  WidgetProcessor.processBorderRadius(node, baseData);

  // Process border width
  WidgetProcessor.processBorderWidth(node, baseData);

  // Process margin for auto layout elements
  // WidgetProcessor.processMargin(node, baseData);

  // Process column settings
  if (baseData.elType === "column") {
    processColumnSettings(node, baseData);
  }

  // Process height
  processHeight(node, baseData, parentHeight);

  // Process children
  if ("children" in node && Array.isArray(node.children)
    && baseData.widgetType !== "button"
    && baseData.widgetType !== "eael-cta-box"
    && baseData.widgetType !== "eael-advanced-menu"
    && baseData.widgetType !== "eael-info-box"
    && baseData.widgetType !== "eael-creative-button"
    && baseData.widgetType?.startsWith('eael') !== true
  ) {
    baseData.elements = node.children.map(child => processNode(child, node?.height));
  }

  // Clean up empty settings
  if (Object.keys(baseData.settings).length === 0) {
    baseData.settings = [];
  }

  return baseData;
}

function processColumnSettings(node: SceneNode, baseData: ElementData): void {
  baseData.settings = {
    _column_size: 100,
    _inline_size: null,
    content_position: "center"
  };

  const columnSizeMap: Record<string, number> = {
    'inner-column-6': 50,
    'inner-column-4': 33.33,
    'inner-column-3': 25
  };

  for (const [key, value] of Object.entries(columnSizeMap)) {
    if (node.name.includes(key)) {
      baseData.settings._column_size = value;
      break;
    }
  }
}

function processHeight(node: SceneNode, baseData: ElementData, parentHeight?: number): void {
  if (!("height" in node) || baseData.elType === "column") return;

  if (node.height !== parentHeight) {
    if (!Array.isArray(baseData.settings)) {
      baseData.settings.height = 'min-height';
      baseData.settings.custom_height = {
        unit: 'px',
        size: node.height,
        sizes: []
      };
    }
  }
}



// Image node from Selection
async function processImageNodes(selection: readonly SceneNode[]) {
  if (selection.length === 0) {
    figma.notify("No nodes selected!");
    return {};
  }

  let nodeIds: string[] = []; // Store all matching node IDs

  // Helper function to collect valid node IDs
  function collectNodeIds(nodes: readonly SceneNode[]) {
    for (const node of nodes) {
      if (node.name.toLocaleLowerCase().startsWith("image") && "fills" in node) {
        // console.log(node.id, node.type, node.name, node)
        nodeIds.push(node.id);
      }

      // Process children recursively
      if ("children" in node && Array.isArray(node.children)) {
        collectNodeIds(node.children);
      }
    }
  }

  // Collect all node IDs from selection
  collectNodeIds(selection);

  if (nodeIds.length === 0) {
    // figma.notify("No matching image nodes found!");
    return {};
  }

  // Fetch image URLs for all collected node IDs
  const imageMap = await processAndUploadFigmaImages(nodeIds);

  return imageMap;
}

// Upload images to remote
async function processAndUploadFigmaImages(nodeIds: string[]) {
  // Step 1: Get Figma image URLs
  const figmaImages = await getFigmaImageUrls(nodeIds);
  // const uploadedImages: { [nodeId: string]: string } = {};

  return figmaImages // Returns an object { nodeId: "figma_url" }

  // Step 2: Upload images to WordPress
  // const uploadedImages = await uploadFigmaImages(figmaImages);

  // return uploadedImages; // Returns an object { nodeId: "uploaded_url" }
}

// Get Image URL from Figma API
async function getFigmaImageUrls(nodeIds: string[]) {
  // const API_KEY = "figd_T5hbshP-ygo-QfMhlNLOwNMPmfsFeMeotJll4Nef";
  const API_KEY = await figma.clientStorage.getAsync('figmaApiKey');
  const FILE_KEY = figma.fileKey;
  if (!FILE_KEY || nodeIds.length === 0) return {};

  const url = `https://api.figma.com/v1/images/${FILE_KEY}?ids=${nodeIds.join(",")}&format=png`;

  try {
    const response = await fetch(url, {
      method: "GET",
      headers: { "X-Figma-Token": API_KEY }
    });

    if (!response.ok) {
      throw new Error(`HTTP error! Status: ${response.status}`);
    }

    const data = await response.json();
    return data.images || {};
  } catch (error) {
    console.error("Error fetching image URLs:", error);
    return {};
  }
}

// Upload images to remote
async function uploadFigmaImages(figmaImageUrls: string) {
  const siteUrl = await figma.clientStorage.getAsync('siteUrl') || '';
  const wpApiUrl = siteUrl.replace(/\/$/, "") + "/wp-json/custom/v1/upload-figma-images/";

  try {
    const response = await fetch(wpApiUrl, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ figma_urls: figmaImageUrls }),
    });

    const data = await response.json();

    if (data.images) {
      return data.images;
    } else {
      console.error("Error uploading image:", data);
    }
  } catch (error) {
    console.error("Request failed:", error);
  }
}

// Get Storeage data and sent to ui
async function sendStoredSettingsToUI() {
  const figmaApiKey = await figma.clientStorage.getAsync('figmaApiKey');

  figma.ui.postMessage({
    type: 'load-settings',
    figmaApiKey
  });
}

// Main plugin code
figma.showUI(__html__, {
  width: 350,
  height: 400
});
sendStoredSettingsToUI()

figma.ui.onmessage = async (msg: { type: string; count: number }) => {
  const { type, message } = msg
  if (!type) {
    return
  }

  const figmaApiKey = await figma.clientStorage.getAsync('figmaApiKey');
  if (type === 'generate-json') {
    if (!figmaApiKey || figmaApiKey !== msg.figmaApiKey) {
      await figma.clientStorage.setAsync('figmaApiKey', msg.figmaApiKey);
      // Optionally do something after storing
      // figma.notify("Settings saved!");
    }

    const selection = figma.currentPage.selection;

    if (selection.length === 0) {
      figma.notify("Please select a section!");
      // figma.closePlugin();
      return;
    }

    const jsonResult = await processSelection(selection); // ✅ Wait for processSelection to complete
    // if (jsonResult) {
    //   figma.ui.resize(500, 600);
    // }

    figma.ui.postMessage({ type: "jsonData", data: jsonResult });
    // figma.ui.postMessage({ type: "exportJson", data: jsonResult });
  }
  else if (message && type === 'notify-message') {
    figma.notify(message);
  }
};