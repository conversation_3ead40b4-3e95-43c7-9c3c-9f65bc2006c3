// Types and interfaces
interface ElementSettings {
    typography_typography?: string;
    typography_font_family?: string;
    typography_font_weight?: string;
    typography_font_size?: FontSize;
    title_color?: string;
    background_background?: string;
    background_color?: string;
    background_color_b?: string;
    border_radius?: BorderRadius;
    height?: string;
    custom_height?: CustomHeight;
    _column_size?: number;
    _inline_size?: null;
    content_position?: string;
    text?: string;
    selected_icon?: IconSettings;
    icon_align?: string;
    title?: string;
    editor?: string;
    header_size?: string;
    eael_cta_title?: string;
    eael_cta_content?: string;
    eael_cta_btn_text?: string;
    eael_cta_secondary_btn_text?: string;
    eael_cta_bg_color?: string;
    eael_cta_title_color?: string;
    eael_cta_title_typography_typography?: string;
    eael_cta_title_typography_font_family?: string;
    eael_cta_title_typography_font_size?: FontSize;
    eael_cta_title_typography_font_weight?: any;
    eael_cta_title_typography_line_height?: any;
    eael_cta_title_typography_line_height_widescreen?: any;
    eael_cta_sub_title_color?: string;
    eael_cta_sub_title_typography_typography?: string;
    eael_cta_sub_title_typography_font_family?: string;
    eael_cta_sub_title_typography_font_size?: FontSize;
    eael_cta_content_color?: string;
    eael_cta_content_typography_typography?: string;
    eael_cta_content_typography_font_family?: string;
    eael_cta_content_typography_font_size?: FontSize;
    eael_cta_container_width_value?: CustomHeight;
    eael_cta_border_border?: string;
    eael_cta_border_width?: BorderRadius;
    eael_cta_border_radius?: any;
    eael_cta_btn_normal_text_color?: string;
    eael_cta_btn_normal_bg_color?: string;
    eael_cat_btn_normal_border_border?: string;
    eael_cat_btn_normal_border_width?: BorderRadius;
    eael_cta_btn_border_radius?: any;
    eael_cta_btn_hover_text_color?: string;
    eael_cta_btn_hover_bg_color?: string;
    eael_cta_btn_icon_size?: CustomHeight;
    eael_infobox_button_background_color?: any;
  
    // Allow any future properties as optional
    [key: string]: any;
}
  
interface ElementData {
    id: string;
    settings: ElementSettings | [];
    elements: ElementData[];
    isInner: boolean;
    widgetType?: string;
    elType: string;
}
  
interface JsonResult {
    content: ElementData[];
    page_settings: any[];
    version: string;
    title: string;
    type: string;
}
  
interface FontSize {
    unit: string;
    size: number;
    sizes: any[];
}
  
interface BorderRadius {
    unit: string;
    top: string;
    right: string;
    bottom: string;
    left: string;
    isLinked: boolean;
}
  
interface CustomHeight {
    unit: string;
    size: number;
    sizes: any[];
}
  
interface IconSettings {
    library: string;
    value: string;
}
  
interface Margin {
    unit: string;
    top: string;
    right: string;
    bottom: string;
    left: string;
    isLinked: boolean;
}
  
interface BoxShadow {
    horizontal: number;
    vertical: number;
    blur: number;
    spread: number;
    color: string;
}

interface FeatureListItem {
    eael_feature_list_title: string;
    eael_feature_list_content: string;
    eael_feature_list_icon_new?: IconSettings;
}