import { SelectionProcessor } from '../processors/selectionProcessor';

export class PluginUI {
  public static initialize() {
    // figma.showUI(__html__);

    // Main plugin code
    figma.showUI(__html__, {
      width: 380,
      height: 400
    });
    this.sendStoredSettingsToUI();
  }

  public static async handleSelectionChange() {
    const selection = figma.currentPage.selection;
    if (selection.length === 0) {
      figma.ui.postMessage({ type: "layer-selected", data: false });
    } else {
      const selectedNode = selection[0];
      if (selectedNode.type === "FRAME" || selectedNode.type === "COMPONENT" || selectedNode.type === "INSTANCE" ||
        selectedNode.type === "GROUP" || selectedNode.type === "RECTANGLE" || selectedNode.type === "ELLIPSE" ||
        selectedNode.type === "POLYGON" || selectedNode.type === "STAR" || selectedNode.type === "VECTOR" ||
        selectedNode.type === "TEXT") {

        try {
          // Export the node as a PNG image
          const bytes = await selectedNode.exportAsync({
            format: 'PNG',
            constraint: { type: 'SCALE', value: 1 } // Export at 2x scale for better quality
          });

          // Convert the bytes to a base64 string
          const base64Image = figma.base64Encode(bytes);
          const imageUrl = `data:image/png;base64,${base64Image}`;

          // Send both the node name and image data URL to the UI
          figma.ui.postMessage({
            type: "layer-selected",
            data: selectedNode.name,
            imageUrl: imageUrl
          });
        } catch (error) {
          console.error("Error exporting node image:", error);
          // If there was an error, just send the name
          figma.ui.postMessage({
            type: "layer-selected",
            data: selectedNode.name,
            error: "Error exporting node image"
          });
        }
      }
      else {
        figma.ui.postMessage({ type: "layer-selected", data: false });
      }
    }
  }

  public static async handleMessage(msg: { type: string; figmaApiKey?: string; message?: string }) {
    const { type, message } = msg;

    if (!type) {
      return;
    }

    if (type === 'save-api-key') {
      console.log('Saving...', msg.figmaApiKey);

      if (!msg.figmaApiKey) {
        figma.ui.postMessage({ type: "api-key-error", error: "Invalid key format" });
        return;
      }
      const storedKey = await figma.clientStorage.getAsync('figmaApiKey');
      if (!storedKey || storedKey !== msg.figmaApiKey) {
        const url = `https://api.figma.com/v1/me`;

        try {
          const response = await fetch(url, {
            method: "GET",
            headers: { "X-Figma-Token": msg.figmaApiKey }
          });

          console.log('Response:', response);

          if (response.status !== 200) {
            figma.ui.postMessage({ type: "api-key-error", error: "API key is not valid" });
            return;
          }
          await figma.clientStorage.setAsync('figmaApiKey', msg.figmaApiKey);
          figma.ui.postMessage({ type: "api-key-success" });
          figma.notify("Figma API Key saved for this project!");
        } catch (error) {
          figma.ui.postMessage({ type: "api-key-error", error: "Invalid key format" });
          return;
        }
      }
      else if (storedKey === msg.figmaApiKey) {
        figma.ui.postMessage({ type: "api-key-success" });
      }
    }

    // Listen for selection changes
    figma.on("selectionchange", () => {
      if (type === 'select-layer') {
        //TODOSend initial message
        // figma.ui.postMessage({
        //   type: "layer-selected",
        //   data: true,
        //   processing: true
        // });
        this.handleSelectionChange()
      }
    });
    if (type === 'select-layer') {
      this.handleSelectionChange();
    }

    if (type === 'generate-json') {
      const figmaApiKey = await figma.clientStorage.getAsync('figmaApiKey');

      if (!figmaApiKey) {
        return;
      }

      const selection = figma.currentPage.selection;

      if (selection.length === 0) {
        figma.notify("Please select a section!");
        return;
      }

      // Reset nodeIds array before collecting new ones
      SelectionProcessor.nodeIds = [];

      // Collect image node IDs from the selection
      SelectionProcessor.collectNodeIds(selection as any);

      // Process images if any image nodes were found
      if (SelectionProcessor.nodeIds.length > 0) {
        try {
          // Fetch image URLs for all collected node IDs
          const imageMap = await this.processAndUploadFigmaImages(SelectionProcessor.nodeIds);
          if (imageMap && Object.keys(imageMap).length > 0) {
            // Update the global ImageArray
            global.ImageArray = imageMap;
          }

        } catch (error) {
          figma.notify('Error processing images. Check console for details.');
        }
      }

      // Process the selection to generate JSON
      const jsonResult = await SelectionProcessor.processSelection(selection as any);

      // Send the JSON data to the UI
      figma.ui.postMessage({ type: "jsonData", data: jsonResult });
    }

    if (message && type === 'notify-message') {
      figma.notify(message);
    }
  }

  //
  // Process and upload images
  public static async processAndUploadFigmaImages(nodeIds: string[]) {
    if (!nodeIds || nodeIds.length === 0) {
      return {};
    }

    try {
      // Step 1: Get Figma image URLs
      const figmaImages = await this.getFigmaImageUrls(nodeIds);

      if (!figmaImages || Object.keys(figmaImages).length === 0) {
        return {};
      }

      // Step 2: Upload images to WordPress (if site URL is configured)
      const siteUrl = await figma.clientStorage.getAsync('siteUrl');

      if (siteUrl) {
        try {
          const uploadedImages = await this.uploadFigmaImages(figmaImages);
          return uploadedImages;
        } catch (error) {
          console.error('Failed to upload images to WordPress:', error);
          figma.notify('Failed to upload images to WordPress. Using Figma URLs instead.');
          return figmaImages;
        }
      } else {
        return figmaImages;
      }
    } catch (error) {
      console.error('Error in processAndUploadFigmaImages:', error);
      figma.notify(`Error processing images: ${error.message}`);
      return {};
    }
  }

  // Get Image URL from Figma API
  public static async getFigmaImageUrls(nodeIds: string[]) {
    const API_KEY = await figma.clientStorage.getAsync('figmaApiKey');
    const FILE_KEY = figma.fileKey;

    if (!FILE_KEY || nodeIds.length === 0) {
      return {};
    }

    if (!API_KEY) {
      figma.notify('Figma API Key is missing. Please enter it in the settings.');
      console.error('Figma API Key is missing');
      return {};
    }

    const url = `https://api.figma.com/v1/images/${FILE_KEY}?ids=${nodeIds.join(",")}&format=png`;

    try {
      const response = await fetch(url, {
        method: "GET",
        headers: { "X-Figma-Token": API_KEY }
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error(`HTTP error! Status: ${response.status}, Response: ${errorText}`);
        figma.notify(`Error fetching images: ${response.status} ${response.statusText}`);
        return {};
      }

      const data = await response.json();

      if (data.err) {
        figma.notify(`Figma API error: ${data.err}`);
        console.error('Figma API error:', data.err);
        return {};
      }

      return data.images || {};
    } catch (error) {
      console.error("Error fetching image URLs:", error);
      figma.notify(`Error fetching images: ${error.message}`);
      return {};
    }
  }

  // Upload images to remote
  public static async uploadFigmaImages(figmaImageUrls: any) {
    const siteUrl = await figma.clientStorage.getAsync('siteUrl') || '';

    if (!siteUrl) {
      figma.notify('WordPress site URL is missing. Please enter it in the settings.');
      console.error('WordPress site URL is missing');
      return figmaImageUrls; // Return the original Figma URLs if we can't upload
    }

    const wpApiUrl = siteUrl.replace(/\/$/, "") + "/wp-json/custom/v1/upload-figma-images/";

    try {
      const response = await fetch(wpApiUrl, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ figma_urls: figmaImageUrls }),
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error(`HTTP error! Status: ${response.status}, Response: ${errorText}`);
        figma.notify(`Error uploading images: ${response.status} ${response.statusText}`);
        return figmaImageUrls; // Return the original Figma URLs if upload fails
      }

      const data = await response.json();

      if (data.images) {
        return data.images;
      } else {
        console.error("Error uploading images:", data);
        figma.notify('Error uploading images to WordPress');
        return figmaImageUrls; // Return the original Figma URLs if upload fails
      }
    } catch (error) {
      console.error("Request failed:", error);
      figma.notify(`Error uploading images: ${error.message}`);
      return figmaImageUrls; // Return the original Figma URLs if upload fails
    }
  }

  // Get Storeage data and sent to ui
  public static async sendStoredSettingsToUI() {
    const figmaApiKey = await figma.clientStorage.getAsync('figmaApiKey');
    if (figmaApiKey) {
      figma.ui.postMessage({
        type: 'load-settings',
        figmaApiKey
      });
    }
  }
}