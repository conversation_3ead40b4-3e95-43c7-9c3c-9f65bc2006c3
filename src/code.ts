import { PluginUI } from './ui/pluginUI';

// Constants
export const PLUGIN_VERSION = "0.5";
export const PLUGIN_TITLE = "figma-to-wp";

// Declare ImageArray as a global variable
declare global {
  var ImageArray: { [nodeId: string]: string };
}

// Initialize the global ImageArray if it doesn't exist
if (typeof global.ImageArray === 'undefined') {
  global.ImageArray = {};
}

// Export a reference to the global ImageArray for convenience
export const ImageArray = global.ImageArray;

// Initialize the plugin UI
PluginUI.initialize();

// Handle messages from the UI
figma.ui.onmessage = async (msg) => {
  await PluginUI.handleMessage(msg);
};