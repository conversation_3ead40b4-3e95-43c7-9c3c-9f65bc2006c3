import { Utils } from '../utils/utils';

export class PostCarouselWidgetProcessor {
  public static process(node: any, data: any) {
    if (!Array.isArray(data.settings)) {
      // Find template node
      const templateNode = node.children?.find(child => child.name === 'template');

      if (templateNode) {
        // Process template elements
        const titleNode = templateNode.children?.find(child => child.name === 'title');
        const excerptNode = templateNode.children?.find(child => child.name === 'excerpt');
        const metaNode = templateNode.children?.find(child => child.name === 'meta');
        const readMoreNode = templateNode.children?.find(child => child.name === 'read-more');

        if (titleNode) {
          Utils.processTitleNode(titleNode, data, 'post_title', 'post-title');
        }

        if (excerptNode) {
          data.settings.show_excerpt = 'yes';
          data.settings.excerpt_length = 15;
        }

        if (metaNode) {
          data.settings.show_meta = 'yes';
          data.settings.meta_data = ['author', 'date', 'comments'];
        }

        if (readMoreNode) {
          data.settings.show_read_more = 'yes';
          data.settings.read_more_text = 'Read More';
        }
      }

      // Set default carousel settings
      data.settings.post_type = 'post';
      data.settings.posts_per_page = 6;
      data.settings.carousel_columns = 3;
      data.settings.carousel_autoplay = 'yes';
      data.settings.carousel_autoplay_speed = 3000;
      data.settings.carousel_loop = 'yes';
      data.settings.carousel_pause_on_hover = 'yes';
    }
  }
} 