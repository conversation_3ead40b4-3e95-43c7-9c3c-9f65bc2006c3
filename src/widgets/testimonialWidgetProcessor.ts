import { Utils } from '../utils/utils';
import { WidgetUtils } from '../utils/widgetUtils';
import { ColorUtils } from '../utils/colorUtils';
import { ElementData, SceneNode, TextNode } from '../types/figma';

export class TestimonialWidgetProcessor {
  public static findNodeByNamePattern(node: SceneNode, pattern: (name: string) => boolean): SceneNode | null {
    if (!node) return null;
    if (pattern(node.name)) return node;

    if ('children' in node) {
      for (const child of node.children) {
        const found = this.findNodeByNamePattern(child, pattern);
        if (found) return found;
      }
    }
    return null;
  }

  public static findNodeByType(node: SceneNode, type: string): SceneNode | null {
    if (!node) return null;
    if (node.type === type) return node;

    if ('children' in node) {
      for (const child of node.children) {
        const found = this.findNodeByType(child, type);
        if (found) return found;
      }
    }
    return null;
  }

  public static process(node: SceneNode, data: ElementData): void {
    console.log('Starting testimonial widget processing for node:', node.name);

    try {
      // Initialize settings if not exists
      if (!data.settings) {
        data.settings = {};
      }

      // Set widget type and default settings
      // data.settings.widgetType = 'eael-testimonial';
      data.settings.eael_testimonial_style = 'content-bottom-icon-title-inline';
      data.settings.eael_testimonial_user_display_block = 'yes';

      // Add new control settings with default values
      data.settings.eael_testimonial_show_quote = ''; // Default to showing quote
      // data.settings.eael_testimonial_rating_position = 'default'; // Will be updated based on design

      // Find all necessary nodes
      const nameNode = Utils.findNodeRecursively(node, undefined, 'name');
      const companyNode = Utils.findNodeRecursively(node, undefined, 'company');
      const contentNode = Utils.findNodeRecursively(node, undefined, 'content');
      const imageNode = Utils.findNodeRecursively(node, undefined, 'image');
      const reviewNode = Utils.findNodeRecursively(node, undefined, 'review');

      // Process name
      if (nameNode) {
        const textNode = this.findNodeByType(nameNode, 'TEXT') as TextNode;
        if (textNode && textNode.characters) {
          data.settings.eael_testimonial_name = Utils.getTextWithCase(textNode);
          WidgetUtils.processTypographySettings(textNode, data, 'eael-testimonial-name');
          WidgetUtils.processTextColor(textNode, data, 'eael-testimonial-name');
        }
      }

      // Process position/company
      if (companyNode) {
        console.log('EA companyNode', companyNode);
        let textNode = this.findNodeByType(companyNode, 'TEXT') as TextNode;

        if (!textNode) {
          textNode = companyNode.type === 'TEXT' ? companyNode : null;
        }

        if (textNode && textNode.characters) {
          data.settings.eael_testimonial_company_title = textNode.characters;
          WidgetUtils.processTypographySettings(textNode, data, 'eael-testimonial-company');
          WidgetUtils.processTextColor(textNode, data, 'eael-testimonial-company');
        }
      }

      // Process content/description
      if (contentNode) {
        const textNode = this.findNodeByType(contentNode, 'TEXT') as TextNode;
        if (textNode && textNode.characters) {
          data.settings.eael_testimonial_description = textNode.characters;
          WidgetUtils.processTypographySettings(textNode, data, 'eael-testimonial-content');
          WidgetUtils.processTextColor(textNode, data, 'eael-testimonial-content');

          // Initialize margin object
          const margin = {
            unit: "px",
            top: "0",
            right: "0",
            bottom: "0",
            left: "0",
            isLinked: false
          };

          // Calculate spacing between content and avatar/name
          if (imageNode || nameNode) {
            const targetNode = imageNode || nameNode;
            const spacing = WidgetUtils.calculateSpacingBetweenNodes(targetNode, contentNode);
            if (spacing) {
              margin.bottom = spacing.toString();
            }
          }

          // Process review/rating spacing
          if (reviewNode) {
            const ratingPosition = WidgetUtils.determineRelativePosition(reviewNode, contentNode);
            data.settings.eael_testimonial_rating_position = ratingPosition;

            const spacing = WidgetUtils.calculateSpacingBetweenNodes(reviewNode, contentNode);
            if (spacing) {
              if (ratingPosition === 'top') {
                margin.top = spacing.toString();
              } else {
                margin.bottom = spacing.toString();
              }
            }
          }

          // Set the final margin
          data.settings.eael_testimonial_description_margin = margin;
        }
      }

      // Process image
      if (imageNode) {
        data.settings.eael_testimonial_image_rounded = 'testimonial-avatar-rounded';
        data.settings.eael_testimonial_image_width = {
          unit: 'px',
          size: imageNode.width,
          sizes: []
        };
        data.settings.eael_testimonial_max_image_width = {
          unit: '%',
          size: imageNode.width,
          sizes: []
        };
        WidgetUtils.processMargin(imageNode, data, 'eael-testimonial-image');
      }

      // Process review/rating
      if (reviewNode) {
        if ('children' in reviewNode) {
          const starNode = reviewNode.children.find(child => child.name.toLowerCase().includes('star'));
          if (starNode) {
            WidgetUtils.processTextColor(starNode, data, 'eael-testimonial-review');
            data.settings.eael_testimonial_rating_item_size = {
              unit: 'px',
              size: starNode.width,
              sizes: []
            };
            WidgetUtils.processMargin(starNode, data, 'eael-testimonial-star');
          }
        }
        WidgetUtils.processMargin(reviewNode, data, 'eael-testimonial-review');
      }

      // Process container background
      WidgetUtils.processBackground(node, data, 'eael-testimonial-container');
      WidgetUtils.processPadding(node, data, 'eael-testimonial-container');

      // Set static settings
      data.settings.eael_testimonial_quotation_color = '';
      data.settings.eael_testimonial_quotation_top = {
        unit: '%',
        size: '',
        sizes: []
      };
      data.settings.eael_testimonial_quotation_right = {
        unit: '%',
        size: '',
        sizes: []
      };

    } catch (error) {
      console.error('Error processing testimonial widget:', error);
      throw error;
    }
  }
}