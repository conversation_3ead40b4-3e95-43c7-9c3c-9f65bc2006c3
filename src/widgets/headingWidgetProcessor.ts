import { WidgetUtils } from '../utils/widgetUtils';
import { Utils } from '../utils/utils';
import { ColorUtils } from '../utils/colorUtils';
import { ElementData, SceneNode, FrameNode, TextNode } from '../types/figma';

export class HeadingWidgetProcessor {
  public static process(node: SceneNode, data: ElementData): void {
    // Process text content and header size
    if (node.type === "TEXT") {
      const textNode = node as TextNode;
      data.settings.title = textNode.characters;
      data.settings.header_size = "h2";

      // Process parent alignment
      const headingParentNode = node.parent;
      let headingParentAlignmentItems;
      let headingParentAlignment = "";
      
      if (headingParentNode && "layoutMode" in headingParentNode) {
        let headingParentLayoutMode = headingParentNode.layoutMode;
        if (headingParentLayoutMode === "VERTICAL" && "counterAxisAlignItems" in headingParentNode) {
          headingParentAlignmentItems = headingParentNode.counterAxisAlignItems;
        } else if (headingParentLayoutMode === "HORIZONTAL" && "counterAxisAlignItems" in headingParentNode) {
          headingParentAlignmentItems = headingParentNode.counterAxisAlignItems;
        }
      }

      headingParentAlignment = headingParentAlignmentItems === "MAX"
        ? "flex-end"
        : headingParentAlignmentItems === "CENTER"
          ? "center"
          : headingParentAlignmentItems === "MIN"
            ? "flex-start"
            : headingParentAlignment;

      // Set vertical alignment
      if (headingParentAlignment) {
        data.settings._flex_align_self = headingParentAlignment;
      }
    }

    // Process typography and text color
    WidgetUtils.processTypographySettings(node, data, "heading");
    WidgetUtils.processTextColor(node, data, "heading");

    // Process height
    if (node.height) {
      data.settings.height = "min-height";
      data.settings.custom_height = {
        unit: "px",
        size: node.height,
        sizes: []
      };
    }
  }
} 