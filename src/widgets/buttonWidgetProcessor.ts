import { Utils } from '../utils/utils';

export class ButtonWidgetProcessor {
  public static process(node: any, data: any) {
    let textNode;
    let iconNode;

    if ("children" in node && Array.isArray(node.children)) {
      textNode = node.children?.find(child => child.type === "TEXT");
      iconNode = node.children?.find(child => child.name.startsWith('icon-'));
    }

    Utils.processTitleNode(textNode, data, 'text', 'button-text');
    Utils.processIconNode(iconNode, data, 'selected_icon');

    data.settings.icon_align =
      "children" in node &&
        node.children &&
        node.children.length >= 2 &&
        node.children[0].type === "TEXT" &&
        node.children[1].name.startsWith("icon-")
        ? "row-reverse"
        : "row";

    const buttonParentNode = node.parent;
    let buttonParentAlignmentItems;
    let buttonParentAlignment = "";
    if (buttonParentNode && "layoutMode" in buttonParentNode) {
      let buttonParentLayoutMode = buttonParentNode.layoutMode;
      if (buttonParentLayoutMode === "VERTICAL" &&
        "counterAxisAlignItems" in buttonParentNode) {
        buttonParentAlignmentItems = buttonParentNode.counterAxisAlignItems;
      }
      else if (buttonParentLayoutMode === "HORIZONTAL" &&
        "counterAxisAlignItems" in buttonParentNode) {
        buttonParentAlignmentItems = buttonParentNode.counterAxisAlignItems;
      }
    }

    buttonParentAlignment =
      buttonParentAlignmentItems === "MAX"
        ? "flex-end"
        : buttonParentAlignmentItems === "CENTER"
          ? "center"
          : buttonParentAlignmentItems === "MIN"
            ? "flex-start"
            : buttonParentAlignment;

    if (buttonParentAlignment) {
      data.settings._flex_align_self = buttonParentAlignment;
    }
  }
} 