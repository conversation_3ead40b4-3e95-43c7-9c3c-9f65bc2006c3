import { WidgetUtils } from '../utils/widgetUtils';
import { Utils } from '../utils/utils';
import { ElementData } from '../types/figma';
// import { ImageArray } from '../code';

export class InfoBoxWidgetProcessor {
  public static process(node: any, data: ElementData) {
    const containerNode = node.name.startsWith("widget") ? node : ""; // widget-eael-info-box
    const titleNode = Utils.findNodeRecursively(node, undefined, "title");
    const subtitleNode = Utils.findNodeRecursively(node, undefined, "subtitle");
    const contentNode = Utils.findNodeRecursively(node, undefined, "content");
    const buttonNode = Utils.findNodeRecursively(node, undefined, "button");
    const mainContentNode = Utils.findNodeRecursively(node, undefined, "main-content");
    const imageNode = Utils.findNodeRecursively(node, undefined, "image");
    const iconNode = Utils.findNodeRecursively(node, undefined, "icon");

    if (!Array.isArray(data.settings)) {
      data.settings.eael_show_infobox_clickable = ""; // default for now

      if (mainContentNode) {
        // process margin of children and inner children
        WidgetUtils.processMargin(mainContentNode, data, "eael-info-box-main-content");
      }

      if (imageNode) {
        data.settings.eael_infobox_img_or_icon = "img";
        const imageParentNode = Utils.findParentNode(node, imageNode) as any;
        const imageParentNodeChildren = imageParentNode?.children;
        const imageIndex = imageParentNodeChildren?.indexOf(imageNode);
        const mainContentNodeIndex = imageParentNodeChildren?.indexOf(mainContentNode);
        let imagePosition = "";

        if (imageIndex >= 0 && mainContentNodeIndex >= 0) {
          const flexDirection = (imageParentNode?.layoutMode) === "HORIZONTAL" ? "row" : "column";
          imagePosition =
            imageIndex < mainContentNodeIndex
              ? flexDirection === "row"
                ? "left"
                : "top"
              : flexDirection === "row"
                ? "right"
                : "bottom";

          let mainContentAlignment = "left";
          if (mainContentNode && "layoutMode" in mainContentNode) {
            let mainContentLayoutMode = mainContentNode.layoutMode;
            let mainContentAlignmentItems: any;

            if (mainContentLayoutMode === "VERTICAL" &&
              "counterAxisAlignItems" in mainContentNode) {
              mainContentAlignmentItems = mainContentNode.counterAxisAlignItems;
            }
            else if (mainContentLayoutMode === "HORIZONTAL" &&
              "primaryAxisAlignItems" in mainContentNode) {
              mainContentAlignmentItems = mainContentNode.primaryAxisAlignItems;
            }

            mainContentAlignment =
              mainContentAlignmentItems === "MAX"
                ? "right"
                : mainContentAlignmentItems === "CENTER"
                  ? "center"
                  : mainContentAlignment;
          }

          let imageParentVerticalAlignment = "middle";
          if (imageParentNode && "layoutMode" in imageParentNode) {
            let imageParentLayoutMode = imageParentNode.layoutMode;
            let imageParentAlignmentItems: any;

            if (imageParentLayoutMode === "VERTICAL" &&
              "counterAxisAlignItems" in imageParentNode) {
              imageParentAlignmentItems = imageParentNode.counterAxisAlignItems;
            }
            else if (imageParentLayoutMode === "HORIZONTAL" &&
              "primaryAxisAlignItems" in imageParentNode) {
              imageParentAlignmentItems = imageParentNode.primaryAxisAlignItems;
            }

            imageParentVerticalAlignment =
              imageParentAlignmentItems === "MAX"
                ? "bottom"
                : imageParentAlignmentItems === "CENTER"
                  ? "middle"
                  : imageParentAlignmentItems === "MIN"
                    ? "top"
                    : imageParentVerticalAlignment;
          }

          if (imagePosition === "right") {
            data.settings.eael_infobox_content_alignment = "left";
            data.settings.eael_infobox_img_type = "img-on-right";
          }
          else if (imagePosition === "left") {
            data.settings.eael_infobox_content_alignment = mainContentAlignment;
            data.settings.eael_infobox_img_type = "img-on-left";
          }
          else if (imagePosition === "top") {
            data.settings.eael_infobox_content_alignment = mainContentAlignment; // content left, center, right
            data.settings.eael_infobox_img_type = "img-on-top";
          }
          else if (imagePosition === "bottom") {
            data.settings.eael_infobox_content_alignment = mainContentAlignment;
            data.settings.eael_infobox_img_type = "img-on-bottom";
          }

          data.settings.icon_vertical_position = imageParentVerticalAlignment;
        }

        // image alignment from constraints
        if ("constraints" in imageNode) {
          const constraints = imageNode.constraints as any;
          if (constraints.vertical === "MIN") {
            data.settings.icon_vertical_position = "top";
          }
          else if (constraints.vertical === "MAX") {
            data.settings.icon_vertical_position = "bottom";
          }
          else if (constraints.vertical === "CENTER") {
            data.settings.icon_vertical_position = "middle";
          }
        }

        // image size
        const imageSize = imageNode.width;
        data.settings.eael_infobox_image_resizer = {
          unit: "px",
          size: imageSize,
          sizes: [],
        };

        // image url
        // Check if the image node has fills
        if ("fills" in imageNode &&
          Array.isArray(imageNode.fills) &&
          imageNode.fills.length > 0) {
          const imageFill = imageNode.fills.find((fill) => fill.type === "IMAGE");
          if (imageFill && global.ImageArray[imageNode.id]) {
            const imageHash = imageFill.imageHash;
            data.settings.eael_infobox_image = {
              // url: `https://api.figma.com/v1/images/${imageHash}`, // not working. we will revamp later.
              // id: "",
              // size: "",
              // alt: "",
              // source: "figma", // not working. we will revamp later
              url: global.ImageArray[imageNode.id],
              size: "",
              alt: "",
              source: "figma",
            };
          }
        }

        WidgetUtils.processBorderRadius(imageNode, data, "eael-info-box-image");
        if (imagePosition !== "right") {
          WidgetUtils.processMargin(imageNode, data, "eael-info-box-image");
        }
      }

      if (iconNode && !imageNode) {
        // if content node is sibling of icon node, then it is icon instead of image. we dont consider icon from main-content frame
        const iconParentNode = Utils.findParentNode(node, iconNode) as any;
        const iconParentNodeChildren = iconParentNode?.children;
        const mainContentNodeIndex = iconParentNodeChildren?.indexOf(mainContentNode);

        if (mainContentNodeIndex >= 0) {
          data.settings.eael_infobox_img_or_icon = "icon";
        }
      }

      if (containerNode) {
        WidgetUtils.processBackground(containerNode, data, "eael-info-box-container");
        WidgetUtils.processPadding(containerNode, data, "eael-info-box-container");
      }

      Utils.processTitleNode(titleNode, data, "eael_infobox_title", "eael-info-box-title");

      // Subtitle
      let args: any = {};
      args.titleTagKey = 'eael_infobox_sub_title_tag';
      args.titleTag = 'h4';

      args.showTitleKey = 'eael_infobox_show_sub_title';
      args.showTitle = 'yes';

      Utils.processTitleNode(subtitleNode, data, "eael_infobox_sub_title", "eael-info-box-subtitle", args);

      if (contentNode &&
        ("children" in contentNode || contentNode.type === "TEXT")) {
        let textItems = [];

        if (contentNode.type === "TEXT") {
          if ("characters" in contentNode) {
            textItems.push(`<p>${contentNode.characters}</p>`);
          }

          WidgetUtils.processTypographySettings(contentNode, data, "eael-info-box-content");
          WidgetUtils.processTextColor(contentNode, data, "eael-info-box-content");
          WidgetUtils.processMargin(contentNode, data, "eael-info-box-content");
        }
        else {
          contentNode.children.forEach((frame) => {
            const frameNode = frame; // Ensure it's treated as a SceneNode

            if (frameNode.type === "FRAME") {
              // Find all TEXT nodes, including in inner children
              const textNodes = Utils.findTextNodes(frameNode);

              if (textNodes.length > 0) {
                textNodes.forEach((textNode) => {
                  if ("characters" in textNode) {
                    textItems.push(`<p>${textNode.characters}</p>`);
                  }
                });

                WidgetUtils.processTypographySettings(textNodes[0], data, "eael-info-box-content");
                WidgetUtils.processTextColor(textNodes[0], data, "eael-info-box-content");
              }
            }
          });
        }

        // Generate the final HTML list
        data.settings.eael_infobox_text = `${textItems.join("")}`;
      }

      // Button processing
      if (buttonNode) {
        // Ensure data.settings is an object, not an array
        const textNodes = Utils.findTextNodes(buttonNode as any);
        let buttonText = []; // unnecessary array; it can be string;

        if (textNodes && textNodes.length > 0) {
          textNodes.forEach((textNode) => {
            if ("characters" in textNode) {
              buttonText.push(`${textNode.characters}`);
            }
          });

          data.settings.infobox_button_text = buttonText.join(" "); // Combine all text nodes into one string
          data.settings.eael_show_infobox_button = "yes";

          WidgetUtils.processTypographySettings(textNodes[0], data, "eael-info-box-button");
          WidgetUtils.processTextColor(textNodes[0], data, "eael-info-box-button");
          WidgetUtils.processBackground(buttonNode, data, "eael-info-box-button");
          WidgetUtils.processPadding(buttonNode, data, "eael-info-box-button");
          WidgetUtils.processBorderRadius(buttonNode, data, "eael-info-box-button");
          WidgetUtils.processBorderWidth(buttonNode, data, "eael-info-box-button");
          // WidgetUtils.processMargin(buttonNode, data, 'eael-info-box-button');
        }

        // get icon node within button starts with icon-
        const buttonIconNode = (buttonNode as any).children?.find((child: any) => child.name.startsWith('icon-'));

        if (buttonIconNode) {
          Utils.processIconNode(buttonIconNode, data, 'eael_infobox_button_icon_new', 'eael_infobox_button_icon_rotate');

          // icon size
          const iconSize = buttonIconNode.width;
          data.settings.eael_infobox_button_icon_size = {
            unit: "px",
            size: iconSize,
            sizes: [],
          };

          // icon indent
          const buttonGap = "itemSpacing" in buttonNode ? buttonNode.itemSpacing : "";
          if (buttonGap) {
            data.settings.eael_infobox_button_icon_indent = {
              unit: "px",
              size: buttonGap,
              sizes: [],
            };
          }

          // icon alignment
          // button node has two child. text and icon. if text is first child then icon on the right
          const iconIndex = (buttonNode as any).children?.indexOf(buttonIconNode);
          const textIndex = (buttonNode as any).children?.indexOf(textNodes[0]);

          if (iconIndex >= 0 && textIndex >= 0) {
            data.settings.eael_infobox_button_icon_alignment =
              iconIndex > textIndex ? "right" : "left";
          }
        }
      }
    }
  }
}