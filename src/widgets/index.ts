import { ButtonWidgetProcessor } from './buttonWidgetProcessor';
import { HeadingWidgetProcessor } from './headingWidgetProcessor';
import { TextEditorWidgetProcessor } from './textEditorWidgetProcessor';
import { CreativeButtonWidgetProcessor } from './creativeButtonWidgetProcessor';
import { DualColorHeadingWidgetProcessor } from './dualColorHeadingWidgetProcessor';
import { AdvancedMenuWidgetProcessor } from './advancedMenuWidgetProcessor';
import { CtaBoxWidgetProcessor } from './ctaBoxWidgetProcessor';
import { InfoBoxWidgetProcessor } from './infoBoxWidgetProcessor';
import { PostCarouselWidgetProcessor } from './postCarouselWidgetProcessor';
import { TestimonialWidgetProcessor } from './testimonialWidgetProcessor';
import { FeatureListWidgetProcessor } from './featureListWidgetProcessor';
import { CounterWidgetProcessor } from './counterWidgetProcessor';
import { LogoCarouselWidgetProcessor } from './logoCarouselWidgetProcessor';
import { WooProductListWidgetProcessor } from './wooProductListWidgetProcessor';
import { ImageWidgetProcessor } from './imageWidgetProcessor';
export {
  ButtonWidgetProcessor,
  HeadingWidgetProcessor,
  TextEditorWidgetProcessor,
  CreativeButtonWidgetProcessor,
  DualColorHeadingWidgetProcessor,
  AdvancedMenuWidgetProcessor,
  CtaBoxWidgetProcessor,
  InfoBoxWidgetProcessor,
  PostCarouselWidgetProcessor,
  TestimonialWidgetProcessor,
  FeatureListWidgetProcessor,
  CounterWidgetProcessor,
  LogoCarouselWidgetProcessor,
  WooProductListWidgetProcessor,
  ImageWidgetProcessor
};