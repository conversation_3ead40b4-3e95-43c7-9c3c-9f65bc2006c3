import { ElementData } from '../types/figma';

export class ImageWidgetProcessor {
  public static process(node: any, data: ElementData) {
    if (!Array.isArray(data.settings) && global.ImageArray && global.ImageArray[node.id]) {
      data.settings = {
        ...data.settings,
        image: {
          url: global.ImageArray[node.id],
          id: '',
          size: '',
          alt: node.name || ''
        }
      };

      console.log(`Image URL set for node ${node.id}:`, global.ImageArray[node.id]);
    } else {
      console.log(`No image URL found for node ${node.id}`);
    }
  }
}