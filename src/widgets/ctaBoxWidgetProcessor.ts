import { WidgetUtils } from '../utils/widgetUtils';
import { Utils } from '../utils/utils';
import { ElementData, SceneNode, FrameNode } from '../types/figma';

export class CtaBoxWidgetProcessor {
  public static process(node: FrameNode, data: ElementData): void {
    const containerNode = node.name.startsWith("widget") ? node : "";
    const titleNode = Utils.findNodeRecursively(node, undefined, "title");
    const contentNode = Utils.findNodeRecursively(node, undefined, "content");
    const buttonNode = Utils.findNodeRecursively(node, undefined, "button");

    // Process container settings
    if (containerNode) {
      WidgetUtils.processBackground(containerNode, data, "eael-cta-box-container");
      WidgetUtils.processPadding(containerNode, data, "eael-cta-box-container");
      if ("parent" in containerNode) {
        WidgetUtils.processMargin(containerNode.parent, data, "eael-cta-box-container");
      }
    }

    // Process title
    Utils.processTitleNode(titleNode, data, 'eael_cta_title', 'eael-cta-box-title');

    // Process content
    if (contentNode && "children" in contentNode) {
      let textItems = [];
      contentNode.children.forEach((frame) => {
        const frameNode = frame;
        if (frameNode.type === "FRAME") {
          const textNodes = Utils.findTextNodes(frameNode);
          if (textNodes.length > 0) {
            textNodes.forEach((textNode) => {
              if ("characters" in textNode) {
                textItems.push(`<li>${textNode.characters}</li>`);
              }
            });
            WidgetUtils.processTypographySettings(textNodes[0], data, "eael-cta-box-content");
            WidgetUtils.processTextColor(textNodes[0], data, "eael-cta-box-content");
          }
        }
      });
      if (!Array.isArray(data.settings)) {
        data.settings.eael_cta_content = `<ul style="list-style: none;">${textItems.join("")}</ul>`;
      }
    } else {
      if (!Array.isArray(data.settings)) {
        data.settings.eael_cta_content = "";
      }
    }

    // Process buttons
    if (buttonNode) {
      // Process primary button
      Utils.processButtonNode(buttonNode, data, 'eael_cta_btn_text', 'eael-cta-box-button');

      // Process secondary button
      const buttonSecondaryNode = Utils.findNodeRecursively(node, undefined, "button-secondary");
      if (buttonSecondaryNode) {
        const textSecondaryNode = (buttonSecondaryNode as FrameNode).children?.find(child => child.type === "TEXT") as SceneNode & { characters?: string };

        if (!Array.isArray(data.settings)) {
          data.settings.eael_cat_secondary_btn_normal_border_border = "none";
          data.settings.eael_cta_secondary_btn_is_show = "yes";
          data.settings.eael_cta_secondary_btn_text = "characters" in textSecondaryNode ? textSecondaryNode.characters : "";
          
          WidgetUtils.processTypographySettings(textSecondaryNode, data, "eael-cta-box-button-secondary");
          WidgetUtils.processTextColor(textSecondaryNode, data, "eael-cta-box-button-secondary");
          WidgetUtils.processBackground(buttonSecondaryNode, data, "eael-cta-box-button-secondary");
        }
      }
    }
  }
} 