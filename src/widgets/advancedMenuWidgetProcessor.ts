import { WidgetUtils } from '../utils/widgetUtils';
import { Utils } from '../utils/utils';
import { ElementData, SceneNode, FrameNode } from '../types/figma';

export class AdvancedMenuWidgetProcessor {
  public static process(node: FrameNode, data: ElementData): void {
    // Process menu ID
    if (data.widgetType.startsWith('eael-advanced-menu') && data.widgetType.length > 18) {
      const menuIdMatch = data.widgetType.match(/(\d+)$/);
      if (menuIdMatch) {
        data.settings.eael_advanced_menu_menu = menuIdMatch[1];
      }
      data.widgetType = 'eael-advanced-menu';
    }

    const menuItemNode = Utils.findNodeRecursively(node, undefined, "menu-item");
    const menuItemActiveNode = Utils.findNodeRecursively(node, undefined, "active-menu-item");
    const containerNode = Utils.findNodeRecursively(node, undefined, "widget-eael-advanced-menu");

    // Process container background
    if (containerNode) {
      WidgetUtils.processBackground(containerNode, data, "eael-advanced-menu-container");
    }

    // Process menu item text and typography
    if (menuItemNode) {
      const menuItemTextNode = (menuItemNode as FrameNode).children?.find(child => child.type === "TEXT") as SceneNode & { characters?: string };
      if (menuItemTextNode) {
        WidgetUtils.processTextColor(menuItemTextNode, data, "eael-advanced-menu-item");
        WidgetUtils.processTypographySettings(menuItemTextNode, data, "eael-advanced-menu-item");
      }
    }

    // Process active menu item text and background
    if (menuItemActiveNode) {
      const menuItemActiveTextNode = (menuItemActiveNode as FrameNode).children?.find(child => child.type === "TEXT") as SceneNode & { characters?: string };
      if (menuItemActiveTextNode) {
        WidgetUtils.processTextColor(menuItemActiveTextNode, data, "eael-advanced-menu-item-hover");
        WidgetUtils.processBackground(menuItemActiveTextNode, data, "eael-advanced-menu-item-hover");
      }
    }

    // Process layout and default settings
    if (!Array.isArray(data.settings)) {
      data.settings.default_eael_advanced_menu_layout = "layoutMode" in node ? node.layoutMode.toLowerCase() : "horizontal";
      WidgetUtils.processBackground(node, data, "eael-advanced-menu-container");
    }

    // Process height
    if (node.height) {
      data.settings.height = 'min-height';
      data.settings.custom_height = {
        unit: 'px',
        size: node.height,
        sizes: []
      };
    }

    // Process margin and padding
    WidgetUtils.processMargin(node, data);
    WidgetUtils.processPadding(node, data);
  }
} 