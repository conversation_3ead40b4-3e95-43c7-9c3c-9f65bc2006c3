import { WidgetUtils } from '../utils/widgetUtils';
import { ColorUtils } from '../utils/colorUtils';
import { TextNode } from '../types/figma';

export class DualColorHeadingWidgetProcessor {
  static process(node: TextNode, data: any): any {
    if (!data.settings) {
      data.settings = {};
    }

    data.settings.eael_dch_first_title = node.characters;
    data.settings.eael_show_dch_icon_content = ""; // static - no icon
    data.settings.eael_dch_last_title = ""; // static - no last title
    data.settings.eael_dch_subtext = ""; // static - no subtext
    // assuming single text node is the dual color heading widget
    if (node.type === "TEXT") {
      WidgetUtils.processTypographySettings(node, data, "eael-dch-first-title");
      
      // Process text color for dual color header
      if (node.fills && Array.isArray(node.fills) && node.fills.length > 0) {
        const fill = node.fills[0];
        if (fill.type === "SOLID" || fill.type === "GRADIENT_LINEAR") {
          const titleColor = ColorUtils.rgbToHex(fill.color);
          data.settings.eael_dch_base_title_color = titleColor;
          data.settings.eael_dch_dual_title_color = titleColor;
        }
      }
    }
    WidgetUtils.processPadding(node, data, "eael-dch-container");
    WidgetUtils.processMargin(node, data, "eael-dch-container");

    return data;
  }
} 