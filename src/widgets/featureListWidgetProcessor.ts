import { WidgetUtils } from '../utils/widgetUtils';
import { Utils } from '../utils/utils';
import { ColorUtils } from '../utils/colorUtils';
import { ElementData, SceneNode, FrameNode } from '../types/figma';

export class FeatureListWidgetProcessor {
  public static process(node: FrameNode, data: ElementData): void {
    if (!node || !data || Array.isArray(data.settings)) {
      return;
    }

    // Initialize feature list array
    data.settings.eael_feature_list = [];
    data.settings.eael_feature_list_connector = "";

    // Find all text nodes for feature items
    const textNodes = Utils.findTextNodes(node);
    
    // Process each text node as a feature item
    textNodes.forEach(textNode => {
      const featureItem = {
        eael_feature_list_title: textNode.characters || '',
        eael_feature_list_content: '',
        eael_feature_list_icon_new: {
          value: 'fas fa-long-arrow-alt-right',
          library: 'fa-solid'
        }
      };
      data.settings.eael_feature_list.push(featureItem);

      // Process typography settings from the first text node
      if (textNodes.indexOf(textNode) === 0) {
        WidgetUtils.processTypographySettings(textNode, data, "eael-feature-list-title");
        WidgetUtils.processTextColor(textNode, data, "eael-feature-list-title");
      }
    });

    // Find icon node
    const iconNode = Utils.findNodeRecursively(node, undefined, 'icon') as FrameNode;
    
    // Set default icon settings
    data.settings.eael_feature_list_icon_color = "";
    data.settings.eael_feature_list_icon_size = {
      unit: "px",
      size: 24,
      sizes: []
    };
    data.settings.eael_feature_list_icon_circle_size = {
      unit: "px",
      size: 24,
      sizes: []
    };
    data.settings.eael_feature_list_icon_space = {
      unit: "px",
      size: "8",
      sizes: []
    };
    data.settings.eael_feature_list_icon_padding = {
      unit: "px",
      top: "6",
      right: "3",
      bottom: "6",
      left: "3",
      isLinked: false
    };

    // Process icon background if icon node exists
    if (iconNode) {
      WidgetUtils.processBackground(iconNode, data, "eael-feature-list-icon");
      WidgetUtils.processMargin(iconNode, data, "eael-feature-list-icon");
      WidgetUtils.processPadding(iconNode, data, "eael-feature-list-icon");
    }

    // Set default margin
    data.settings.margin = {
      unit: "px",
      top: "0",
      right: "8",
      bottom: "0",
      left: "0",
      isLinked: false
    };

    // Set icon rotation
    data.settings.eael_infobox_button_icon_rotate = {
      unit: "deg",
      size: -240,
      sizes: []
    };

    // Set height
    if (node.height) {
      data.settings.height = "min-height";
      data.settings.custom_height = {
        unit: "px",
        size: node.height,
        sizes: []
      };
    }

    // Add general typography settings
    data.settings.typography_typography = 'custom';
  }
} 