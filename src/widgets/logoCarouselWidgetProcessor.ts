import { Utils } from '../utils/utils';

export class LogoCarouselWidgetProcessor {
  public static process(node: any, data: any) {
    if (!Array.isArray(data.settings)) {
      data.settings.logo_carousel_items = [];

      if ("children" in node && Array.isArray(node.children)) {
        const logoItems = node.children.filter(child => child.type === "FRAME" && child.name.startsWith('logo-item-'));
        
        logoItems.forEach((logoItem) => {
          const imageNode = logoItem.children?.find(child => child.type === "RECTANGLE" || child.type === "IMAGE");

          if (imageNode) {
            const logoItemData = {
              logo_image: {
                url: imageNode.fills?.[0]?.imageRef || '',
                id: '',
                size: '',
                alt: logoItem.name
              },
              logo_link: {
                url: "#",
                is_external: false,
                nofollow: false
              }
            };

            data.settings.logo_carousel_items.push(logoItemData);
          }
        });
      }

      // Set default logo carousel settings
      data.settings.carousel_columns = 5;
      data.settings.carousel_autoplay = 'yes';
      data.settings.carousel_autoplay_speed = 3000;
      data.settings.carousel_loop = 'yes';
      data.settings.carousel_pause_on_hover = 'yes';
      data.settings.carousel_grayscale = 'yes';
      data.settings.carousel_grayscale_hover = 'no';
    }
  }
} 