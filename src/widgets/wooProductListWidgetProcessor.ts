import { Utils } from '../utils/utils';

export class WooProductListWidgetProcessor {
  public static process(node: any, data: any) {
    if (!Array.isArray(data.settings)) {
      // Find template node
      const templateNode = node.children?.find(child => child.name === 'template');

      if (templateNode) {
        // Process template elements
        const titleNode = templateNode.children?.find(child => child.name === 'title');
        const priceNode = templateNode.children?.find(child => child.name === 'price');
        const ratingNode = templateNode.children?.find(child => child.name === 'rating');
        const cartButtonNode = templateNode.children?.find(child => child.name === 'add-to-cart');

        if (titleNode) {
          Utils.processTitleNode(titleNode, data, 'product_title', 'product-title');
        }

        if (priceNode) {
          data.settings.show_price = 'yes';
        }

        if (ratingNode) {
          data.settings.show_rating = 'yes';
        }

        if (cartButtonNode) {
          data.settings.show_add_to_cart = 'yes';
          data.settings.add_to_cart_text = 'Add to Cart';
        }
      }

      // Set default product list settings
      data.settings.product_type = 'all';
      data.settings.products_per_page = 9;
      data.settings.columns = 3;
      data.settings.order = 'desc';
      data.settings.orderby = 'date';
      data.settings.show_category = 'yes';
      data.settings.show_stock_status = 'yes';
      data.settings.show_sale_badge = 'yes';
      data.settings.sale_badge_text = 'Sale!';
    }
  }
} 