import { WidgetUtils } from './widgetUtils';
import { SceneNode, TextNode, FrameNode, ElementData } from '../types/figma';

interface ProcessArgs {
  titleTagKey?: string;
  titleTag?: string;
  showTitleKey?: string;
  showTitle?: string;
  showButtonKey?: string;
  showButton?: string;
}

export class Utils {
  public static findNodeRecursively(node: SceneNode, parentNode: SceneNode | undefined, nodeName: string): SceneNode | null {
    if (!node) return null;

    if (node.name.toLowerCase().startsWith(nodeName.toLowerCase())) {
      return node;
    }

    if ('children' in node && node.children) {
      for (const child of node.children) {
        const foundNode = Utils.findNodeRecursively(child, node, nodeName);
        if (foundNode) return foundNode;
      }
    }

    return null;
  }

  public static findParentNode(node: SceneNode, childNode: SceneNode): SceneNode | null {
    if (!node || !childNode) return null;

    if ('children' in node && node.children) {
      if (node.children.includes(childNode)) {
        return node;
      }

      for (const child of node.children) {
        const foundParent = Utils.findParentNode(child, childNode);
        if (foundParent) return foundParent;
      }
    }

    return null;
  }

  public static findTextNodes(node: FrameNode): TextNode[] {
    const textNodes: TextNode[] = [];

    if (!node || !('children' in node) || !node.children) {
      return textNodes;
    }

    for (const child of node.children) {
      if (child.type === 'TEXT') {
        textNodes.push(child);
      } else if ('children' in child) {
        textNodes.push(...Utils.findTextNodes(child as FrameNode));
      }
    }

    return textNodes;
  }

  public static getTextWithCase(textNode: any): string {
    if (!("characters" in textNode)) return "";

    let text = textNode.characters;
    let caseType = textNode.textCase;

    switch (caseType) {
      case "UPPER":
        text = text.toUpperCase();
        break;
      case "LOWER":
        text = text.toLowerCase();
        break;
      case "TITLE":
        text = text.replace(/\b\w/g, (char) => char.toUpperCase());
        break;
      case "SMALL_CAPS":
      case "SMALL_CAPS_FORCED":
        // Small caps may require custom font handling
        console.warn("Small caps not directly supported in JavaScript");
        break;
      default:
        // Keep original case
        break;
    }

    return text;
  }

  public static processTitleNode(titleNode: SceneNode, data: ElementData, titleKey?: string, widgetNodeType?: string, args?: ProcessArgs) {
    if (!titleNode || !data || Array.isArray(data.settings)) {
      return;
    }

    let textNode: TextNode | null = null;

    if ('children' in titleNode && titleNode.children) {
      const foundNode = titleNode.children.find(child => child?.type === "TEXT");
      if (foundNode && foundNode.type === "TEXT") {
        textNode = foundNode;
      }
    } else if (titleNode.type === "TEXT") {
      textNode = titleNode;
    }

    if (textNode) {
      if (titleKey) {
        data.settings[titleKey] = Utils.getTextWithCase(textNode);
      }

      if (args) {
        if (args.titleTagKey && args.titleTag) {
          data.settings[args.titleTagKey] = args.titleTag;
        }

        if (args.showTitleKey && args.showTitle) {
          data.settings[args.showTitleKey] = args.showTitle;
        }
      }

      if (widgetNodeType) {
        WidgetUtils.processTypographySettings(textNode, data, widgetNodeType);
        WidgetUtils.processTextColor(textNode, data, widgetNodeType);
        if (titleNode.type === 'FRAME') {
          WidgetUtils.processMargin(titleNode, data, widgetNodeType);
        }
      }
    }
  }

  public static processIconNode(iconNode: SceneNode, data: ElementData, iconKey?: string, rotationKey?: string) {
    if (!iconNode) return;

    let iconName = iconNode.name.substring(5);
    // Extract rotation if exists
    const rotationMatch = iconName.match(/-rotation(-?\d+)$/);
    if (rotationMatch) {
      if (rotationKey) {
        data.settings[rotationKey] = {
          unit: "deg",
          size: parseInt(rotationMatch[1], 10),
          sizes: []
        };
      }
      // Remove the "-rotationXXX" part from the icon name
      iconName = iconName.replace(/-rotation(-?\d+)$/, "");
    }

    if (iconKey) {
      data.settings[iconKey] = {
        value: `fas ${iconName}`,
        library: "fa-solid",
      };
    }
  }

  public static processButtonNode(buttonNode: SceneNode, data: ElementData, buttonKey?: string, widgetNodeType?: string, args?: ProcessArgs) {
    if (!buttonNode) return;

    const textNodes = Utils.findTextNodes(buttonNode as FrameNode);
    const buttonText: string[] = [];

    if (textNodes.length > 0) {
      textNodes.forEach((textNode) => {
        buttonText.push(textNode.characters);
      });

      if (buttonKey) {
        data.settings[buttonKey] = buttonText.join(" ");
      }

      if (args) {
        if (args.showButtonKey && args.showButton) {
          data.settings[args.showButtonKey] = args.showButton;
        }
      }

      if (widgetNodeType && buttonNode.type === 'FRAME') {
        WidgetUtils.processTypographySettings(textNodes[0], data, widgetNodeType);
        WidgetUtils.processTextColor(textNodes[0], data, widgetNodeType);
        WidgetUtils.processBackground(buttonNode, data, widgetNodeType);
        WidgetUtils.processPadding(buttonNode, data, widgetNodeType);
        WidgetUtils.processBorderRadius(buttonNode, data, widgetNodeType);
        WidgetUtils.processBorderWidth(buttonNode, data, widgetNodeType);
      }
    }
  }
} 