import { Utils } from './utils';
import { SceneNode, FrameNode, GroupNode, TextNode } from '../types/figma';

export class ElementTypeResolver {
  static getElementType(node: SceneNode): string {
    const typeMap: Record<string, string> = {
      'FRAME': 'section',
      'GROUP': 'column',
      'TEXT': 'widget'
    };

    let type = typeMap[node.type] || 'widget';

    if (node.type === 'FRAME') {
      const frameNode = node as FrameNode;
      if (frameNode.name.toLowerCase().startsWith('container')) {
        type = 'container';
        return type;
      }

      if (frameNode.name.toLowerCase().startsWith('widget-')) {
        type = 'widget';
      }
    }

    const specialNames = ['section', 'inner-section', 'column', 'inner-column',
      'inner-column-6', 'inner-column-4', 'inner-column-3'];

    if (specialNames.includes(node.name)) {
      type = node.name.includes('column') ? 'column' : 'section';
    }

    // Find container parent by checking if any parent node has 'container' in its name
    let parent = node;
    while (parent && 'parent' in parent) {
      if (parent.name.toLowerCase().includes('container')) {
        type = type === 'section' ? 'container' : type;
        break;
      }
      parent = parent.parent as SceneNode;
    }

    return type;
  }
} 