import { ColorUtils } from './colorUtils';

export class WidgetUtils {
  public static processWidgetSettings(node: any, data: any) {
    if (!Array.isArray(data.settings)) {
      // Process basic settings
      if (node.type === "TEXT") {
        data.settings.text = node.characters;
      }

      // Process layout settings
      if (node.layoutMode) {
        data.settings.flex_direction = node.layoutMode === 'HORIZONTAL' ? 'row' : 'column';
      }

      // Process alignment
      if (node.primaryAxisAlignItems) {
        const alignment = node.primaryAxisAlignItems;
        if (alignment === 'SPACE_BETWEEN') {
          data.settings.flex_justify_content = 'space-between';
        } else if (alignment === 'CENTER') {
          data.settings.flex_justify_content = 'center';
        } else if (alignment === 'MIN') {
          data.settings.flex_justify_content = 'flex-start';
        } else if (alignment === 'MAX') {
          data.settings.flex_justify_content = 'flex-end';
        }
      }
    }
  }

  public static processTypographySettings(node: any, data: any, widgetNodeType: string) {
    if (!Array.isArray(data.settings)) {
      const typography = {
        typography: 'custom',
        fontFamily: node.fontName?.family,
        fontWeight: node.fontName?.style === "Regular" ? "400"
          : node.fontName?.style === "Medium" ? "500"
            : node.fontName?.style === "SemiBold" ? "600"
              : node.fontName?.style === "Bold" ? "700"
                : node.fontName?.style === "ExtraBold" ? "800"
                  : node.fontName?.style === "Black" ? "900"
                    : node.fontName?.style,
        fontSize: node.fontSize ? { unit: "px", size: node.fontSize, sizes: [] } : undefined,
        lineHeight: node.lineHeight ? { unit: node.lineHeight.unit === "PERCENT" ? "%" : "px", size: node.lineHeight.value, sizes: [] } : undefined,
        textTransform: node.textCase?.toLowerCase() || 'none',
        letterSpacing: node.letterSpacing ? { unit: node.letterSpacing.unit, size: node.letterSpacing.value, sizes: [] } : undefined,
      };

      // if original then, change to none;
      typography.textTransform = (node.textCase === "ORIGINAL" || node.textCase === 'TITLE') ? 'none' : typography.textTransform;

      const widgetMap: Record<string, Record<string, string>> = {
        'eael-creative-button': {
          'eael-creative-button-text': 'eael_creative_button_typography',
        },
        'eael-dual-color-header': {
          'eael-dch-first-title': 'eael_dch_first_title_typography',
        },
        'eael-advanced-menu': {
          'eael-advanced-menu-item': 'default_eael_advanced_menu_item_typography',
        },
        'eael-cta-box': {
          'eael-cta-box-title': 'eael_cta_title_typography',
          'eael-cta-box-content': 'eael_cta_content_typography',
          'eael-cta-box-button': 'eael_cta_btn_typography',
          'eael-cta-box-button-secondary': 'eael_cta_secondary_btn_typography',
        },
        'eael-info-box': {
          'eael-info-box-title': 'eael_infobox_title_typography',
          'eael-info-box-subtitle': 'eael_infobox_sub_title_typography',
          'eael-info-box-content': 'eael_infobox_content_typography_hover',
          'eael-info-box-button': 'eael_infobox_button_typography',
        },
        'eael-post-carousel': {
          'eael-post-carousel-title': 'eael_post_grid_title_typography',
          'eael-post-carousel-content': 'eael_post_grid_excerpt_typography',
        },
        'eael-testimonial': {
          'eael-testimonial-name': 'eael_testimonial_name_typography',
          'eael-testimonial-company': 'eael_testimonial_position_typography',
          'eael-testimonial-content': 'eael_testimonial_description_typography',
        },
        'eael-feature-list': {
          'eael-feature-list-title': 'eael_feature_list_title_typography',
        },
        'eael-counter': {
          'eael-counter-number': 'counter_num_typography',
          'eael-counter-title': 'counter_title_typography',
          'eael-counter-suffix': 'section_number_suffix_typography',
        },
      };

      if (data.widgetType && widgetNodeType && widgetMap[data.widgetType]?.[widgetNodeType]) {
        const prefix = widgetMap[data.widgetType][widgetNodeType];
        data.settings[`${prefix}_typography`] = typography.typography;
        data.settings[`${prefix}_font_family`] = typography.fontFamily;

        // Special case for Advanced Menu - ensure it gets the correct font weight
        if (data.widgetType === 'eael-advanced-menu' && widgetNodeType === 'eael-advanced-menu-item') {
          data.settings[`${prefix}_font_weight`] = "400"; // Force 400 (Regular) for Advanced Menu
        } else {
          data.settings[`${prefix}_font_weight`] = typography.fontWeight;
        }

        if (typography.fontSize) data.settings[`${prefix}_font_size`] = typography.fontSize;
        if (typography.lineHeight) data.settings[`${prefix}_line_height`] = typography.lineHeight;
        if (typography.textTransform) data.settings[`${prefix}_text_transform`] = typography.textTransform;
        if (typography.letterSpacing) data.settings[`${prefix}_letter_spacing`] = typography.letterSpacing;

        if (data.widgetType === 'eael-info-box' && widgetNodeType === 'eael-info-box-button') {
          // data.settings[`${prefix}_text_decoration`] = 'underline'; // Static for now
        }

        if (widgetNodeType === 'eael-advanced-menu-item') {
          data.settings[`${prefix}_line_height_widescreen`] = typography.lineHeight;
          data.settings[`${prefix}_line_height_laptop`] = typography.lineHeight;
          data.settings[`${prefix}_line_height_tablet_extra`] = typography.lineHeight;
          data.settings[`${prefix}_line_height_tablet`] = typography.lineHeight;
          // data.settings[`${prefix}_line_height_mobile_extra`] = typography.lineHeight; // may be figma design line height is not perfect for mobile line height?
          // data.settings[`${prefix}_line_height_mobile`] = typography.lineHeight;
        }
      } else {
        data.settings.typography_typography = typography.typography;
        data.settings.typography_font_family = typography.fontFamily;
        data.settings.typography_font_weight = typography.fontWeight;
        if (typography.fontSize) data.settings.typography_font_size = typography.fontSize;
        if (typography.lineHeight) data.settings.typography_line_height = typography.lineHeight;
      }
    }
  }

  public static processTextColor(node: any, data: any, settingKey: string) {
    if (node.type !== "TEXT" || !("fills" in node) || !Array.isArray(node.fills) || node.fills.length === 0) {
      return;
    }

    const fill = node.fills[0];
    if (!["SOLID", "GRADIENT_LINEAR"].includes(fill.type) || Array.isArray(data.settings)) {
      return;
    }

    const titleColor = ColorUtils.rgbToHex(fill.color);
    const widgetType = data.widgetType;

    const widgetSettingsMap: Record<string, Record<string, string>> = {
      "button": {
        "button-text": "button_text_color",
      },
      "eael-creative-button": {
        "eael-creative-button-text": "eael_creative_button_text_color",
      },
      "eael-dual-color-header": {
        "eael-dch-first-title": "eael_dch_base_title_color",
      },
      "eael-advanced-menu": {
        "eael-advanced-menu-item": "default_eael_advanced_menu_item_color",
        "eael-advanced-menu-item-hover": "default_eael_advanced_menu_item_color_hover"
      },
      "eael-cta-box": {
        "eael-cta-box-title": "eael_cta_title_color",
        "eael-cta-box-content": "eael_cta_content_color",
        "eael-cta-box-button": "eael_cta_btn_normal_text_color",
        "eael-cta-box-button-secondary": "eael_cta_secondary_btn_normal_text_color",
      },
      "eael-info-box": {
        "eael-info-box-title": "eael_infobox_title_color",
        "eael-info-box-subtitle": "eael_infobox_sub_title_color",
        "eael-info-box-content": "eael_infobox_content_color",
        "eael-info-box-button": "eael_infobox_button_text_color"
      },
      "eael-post-carousel": {
        "eael-post-carousel-title": "eael_post_grid_title_color",
        "eael-post-carousel-content": "eael_post_grid_excerpt_color",
      },
      "eael-testimonial": {
        'eael-testimonial-name': 'eael_testimonial_name_color',
        'eael-testimonial-company': 'eael_testimonial_company_color',
        'eael-testimonial-content': 'eael_testimonial_description_color',
        "eael-testimonial-review": "eael_testimonial_rating_item_color",
      },
      "eael-feature-list": {
        'eael-feature-list-title': 'eael_feature_list_title_color',
      },
      "eael-counter": {
        'eael-counter-title': 'counter_title_color',
        'eael-counter-number': 'counter_num_color',
      }
    };

    if (widgetType && settingKey && widgetSettingsMap[widgetType]?.[settingKey]) {
      data.settings[widgetSettingsMap[widgetType][settingKey]] = titleColor;

      // Handle special cases
      if (widgetType === "eael-cta-box" && settingKey === "eael-cta-box-button") {
        data.settings.eael_cta_btn_hover_text_color = ""; // static for now
      }

      if (settingKey === 'eael-cta-box-button-secondary') {
        data.settings.eael_cta_secondary_btn_hover_text_color = titleColor;
      }

      if (widgetType === "eael-info-box" && settingKey === "eael-info-box-button") {
        data.settings.eael_infobox_button_hover_text_color = titleColor;
      }

      if (settingKey === "eael-creative-button-text") {
        data.settings.eael_creative_button_hover_text_color = titleColor;
        data.settings.eael_creative_button_icon_color = titleColor;
        data.settings.eael_creative_button_hover_icon_color = titleColor;
      }

      if (settingKey === "eael-dch-first-title") {
        data.settings.eael_dch_dual_title_color = titleColor;
      }

      if (settingKey === "eael-advanced-menu-item") {
        data.settings.default_eael_advanced_menu_item_indicator_color = titleColor;
        data.settings.default_eael_advanced_menu_dropdown_item_color = titleColor;
        data.settings.default_eael_advanced_menu_dropdown_item_indicator_color = titleColor;
      }

      if (settingKey === "eael-advanced-menu-item-hover") {
        data.settings.default_eael_advanced_menu_item_indicator_color_hover = titleColor;
        data.settings.default_eael_advanced_menu_dropdown_item_color_hover = titleColor;
        data.settings.default_eael_advanced_menu_dropdown_item_indicator_color_hover = titleColor;
      }

      if (settingKey === "eael-post-carousel-title") {
        data.settings.eael_post_grid_title_hover_color = titleColor;
      }

      if (settingKey === "eael-counter-number") {
        data.settings.section_number_suffix_color = titleColor;
      }
    } else {
      data.settings.title_color = titleColor;
    }
  }

  public static processBackground(node: any, data: any, settingKey?: string) {

    if (node.type === "TEXT" || !("fills" in node) || !Array.isArray(node.fills) || node.fills.length === 0) {
      console.log('Skipping background processing - invalid node or no fills');
      if (settingKey === "eael-info-box-button" && !Array.isArray(data.settings)) {
        data.settings.eael_infobox_button_background_color = "";
        data.settings.eael_infobox_button_hover_background_color = "";
      }
      return;
    }

    const fill = node.fills[0];
    if (Array.isArray(data.settings)) {
      console.log('Skipping background processing - settings is an array');
      return;
    }

    let backgroundType = fill.type === "SOLID" ? "classic" : "gradient";
    let backgroundColor = ColorUtils.rgbToHex(fill.color);
    let backgroundColorB = fill.type === "GRADIENT_LINEAR" ? ColorUtils.rgbToHex(fill.gradientStops[1]?.color) : "";

    if (!backgroundColor) {
      backgroundColor = backgroundColorB;
    }

    const widgetSettingsMap: Record<string, Record<string, string>> = {
      "eael-creative-button": {
        "eael-creative-button-container": "eael_creative_button_background_color"
      },
      "eael-advanced-menu": {
        "eael-advanced-menu-container": "default_eael_advanced_menu_background",
        "eael-advanced-menu-item-hover": "default_eael_advanced_menu_item_background_hover"
      },
      "eael-cta-box": {
        "eael-cta-box-container": "eael_cta_bg_color",
        "eael-cta-box-button": "eael_cta_btn_normal_bg_color",
        "eael-cta-box-button-secondary": "eael_cta_secondary_btn_normal_bg_color_color",
      },
      "eael-info-box": {
        "eael-info-box-container": "eael_section_infobox_container_bg",
        "eael-info-box-button": "eael_infobox_button_background_color"
      },
      "eael-post-carousel": {
        "eael-post-carousel-post": "eael_post_grid_bg_color"
      },
      "eael-testimonial": {
        "eael-testimonial-container": "eael_testimonial_background"
      },
      "eael-feature-list": {
        "eael-feature-list-icon": "eael_feature_list_icon_background_color"
      },
      "eael-counter": {
        "eael-counter-container": "_background_color"
      },
      "eael-woo-product-list": {
        "eael-woo-product-list-container": "eael_product_list_container_normal_background_color"
      },
    };

    if (data.widgetType && settingKey && widgetSettingsMap[data.widgetType]?.[settingKey]) {
      data.settings[widgetSettingsMap[data.widgetType][settingKey]] = backgroundColor;

      // Handle special cases
      if (data.widgetType === "eael-info-box" && settingKey === "eael-info-box-button") {
        data.settings.eael_infobox_button_background_color_color = backgroundColor;
        data.settings.eael_infobox_button_hover_background_color_color = backgroundColor;
        data.settings.eael_infobox_button_hover_background_color = backgroundColor;

        if (backgroundColorB) {
          data.settings.eael_infobox_button_background_color_color_b = backgroundColorB;
          data.settings.eael_infobox_button_hover_background_color_color_b = backgroundColorB;
          data.settings.eael_infobox_button_background_color_b = backgroundColorB;
          data.settings.eael_infobox_button_hover_background_color_b = backgroundColorB;
        }
      } else if (settingKey === "eael-creative-button-container") {
        data.settings.eael_creative_button_hover_background_color = backgroundColor;
      }

      if (settingKey === "eael-cta-box-button") {
        data.settings.eael_cta_btn_hover_bg_color = backgroundColor;
      }

      if (settingKey === "eael-cta-box-button-secondary") {
        data.settings.eael_cta_secondary_btn_normal_bg_color_background = 'classic';
        data.settings.eael_cta_secondary_btn_hover_bg_color_background = 'classic';
        data.settings.eael_cta_secondary_btn_hover_bg_color_color = backgroundColor;
      }

      if (settingKey === "eael-advanced-menu-container") {
        data.settings.default_eael_advanced_menu_item_background_hover = backgroundColor;
      }

      if (settingKey === "eael-feature-list-icon") {
        data.settings.eael_feature_list_icon_background_background = 'classic';
      }

      if (settingKey === "eael-counter-container") {
        data.settings._background_background = 'classic';
      }

      if (settingKey === "eael-woo-product-list-container") {
        data.settings.eael_product_list_container_normal_background_background = 'classic';
      }
    } else {
      console.log('Setting default background:', {
        background_background: backgroundType,
        background_color: backgroundColor,
        background_color_b: backgroundColorB
      });
      data.settings.background_background = backgroundType;
      data.settings.background_color = backgroundColor;
      if (backgroundColorB) {
        data.settings.background_color_b = backgroundColorB;
      }
    }
  }

  public static processBorderRadius(node: any, data: any, settingKey?: string) {
    if (!("cornerRadius" in node) || node.cornerRadius === 0) return;
    if (Array.isArray(data.settings)) return;

    const cornerRadius = node.cornerRadius ?? 0;

    let border_radius = {
      unit: "px",
      top: cornerRadius.toString(),
      right: cornerRadius.toString(),
      bottom: cornerRadius.toString(),
      left: cornerRadius.toString(),
      isLinked: true
    };

    let border_radius_v2 = {
      unit: "px",
      size: cornerRadius.toString(),
      sizes: [],
    };

    // Map widget types to their border radius property names
    const widgetSettingsMap: Record<string, Record<string, string>> = {
      "eael-cta-box": {
        "eael-cta-box-button": "eael_cta_btn_border_radius",
        "eael-cta-box-container": "eael_cta_border_radius"
      },
      "eael-info-box": {
        "eael-info-box-button": "eael_infobox_button_border_radius",
        "eael-info-box-image": "eael_infobox_img_shape_radius"
      },
      "eael-creative-button": {
        "eael-creative-button-container": "eael_creative_button_border_radius"
      },
      "eael-post-carousel": {
        "eael-post-carousel-image": "eael_thumbnail_border_radius",
        "eael-post-carousel-post": "eael_post_grid_border_radius"
      },
      "eael-counter": {
        "eael-counter-icon": "counter_icon_border_radius",
        "eael-counter-container": "_border_radius"
      }
    };

    // Set the border radius based on widget type and node type
    if (data.widgetType && settingKey && widgetSettingsMap[data.widgetType]?.[settingKey]) {
      // Special cases that need v2 format
      if (
        (data.widgetType === 'eael-cta-box' && settingKey === 'eael-cta-box-button') ||
        (data.widgetType === 'eael-cta-box' && settingKey === 'eael-cta-box-container') ||
        (data.widgetType === 'eael-info-box' && settingKey === 'eael-info-box-button') ||
        (data.widgetType === 'eael-creative-button' && settingKey === 'eael-creative-button-container')
      ) {
        data.settings[widgetSettingsMap[data.widgetType][settingKey]] = border_radius_v2;
      }
      // Special case for info box image
      else if (data.widgetType === 'eael-info-box' && settingKey === 'eael-info-box-image') {
        data.settings.eael_infobox_img_shape = 'radius';
        data.settings[widgetSettingsMap[data.widgetType][settingKey]] = border_radius;
      }
      // Default case
      else {
        data.settings[widgetSettingsMap[data.widgetType][settingKey]] = border_radius;
      }
    } else {
      data.settings.border_radius = border_radius;
    }
  }

  public static processBorderWidth(node: any, data: any, settingKey?: string) {

    // Check if node has strokeWeight and if it's greater than 0
    if (!("strokeWeight" in node) || !node.strokeWeight) {
      return;
    }

    // Check if settings is an array
    if (Array.isArray(data.settings)) {
      return;
    }

    // Check if strokes are actually visible in the Figma design
    // Only apply border if strokes exist, are visible, and have non-zero opacity
    const hasVisibleStrokes = node.strokes &&
      node.strokes.length > 0 &&
      (node.strokes[0].visible !== false) &&
      (node.strokes[0].opacity !== 0);

    // console.log('Stroke visibility check:', {
    //   hasStrokes: !!node.strokes,
    //   strokesLength: node.strokes?.length,
    //   visible: node.strokes?.[0]?.visible,
    //   opacity: node.strokes?.[0]?.opacity,
    //   hasVisibleStrokes
    // });

    // If no visible strokes, don't apply any border
    if (!hasVisibleStrokes) {
      return;
    }

    const borderWidth = {
      unit: "px",
      top: node.strokeWeight.toString(),
      right: node.strokeWeight.toString(),
      bottom: node.strokeWeight.toString(),
      left: node.strokeWeight.toString(),
      isLinked: true
    };

    // Map widget types to their border width property names
    const widgetSettingsMap: Record<string, Record<string, string>> = {
      "eael-cta-box": {
        "eael-cta-box-button": "eael_cat_btn_normal_border_width",
        "eael-cta-box-container": "eael_cta_border_width"
      },
      "eael-info-box": {
        "eael-info-box-button": "eael_infobox_button_border_width"
      }
    };

    // Set the border width based on widget type and node type
    if (data.widgetType && settingKey && widgetSettingsMap[data.widgetType]?.[settingKey]) {
      // Set border width
      data.settings[widgetSettingsMap[data.widgetType][settingKey]] = borderWidth;

      // Set border style
      if (data.widgetType === 'eael-cta-box') {
        if (settingKey === 'eael-cta-box-button') {
          data.settings.eael_cat_btn_normal_border_border = 'solid';
        } else {
          data.settings.eael_cta_border_border = 'solid';
        }
      } else if (data.widgetType === 'eael-info-box' && settingKey === 'eael-info-box-button') {
        data.settings.eael_infobox_button_border_border = 'solid';

        // Set border color if available
        if (node.strokes && node.strokes.length > 0 && 'color' in node.strokes[0]) {
          data.settings.eael_infobox_button_border_color = ColorUtils.rgbToHex(node.strokes[0].color);
        }
      }
    } else {
      console.log('Applying default border:', {
        borderWidth
      });

      data.settings.border_width = borderWidth;
      data.settings.border_border = 'solid';
      data.settings.border_color = ColorUtils.rgbToHex(node.strokes[0].color);
    }
  }

  public static processPadding(node: any, data: any, settingKey?: string) {
    // We need to check if the node has padding properties
    if (!("paddingLeft" in node) && !("paddingRight" in node) &&
      !("paddingTop" in node) && !("paddingBottom" in node)) {
      return;
    }

    if (Array.isArray(data.settings)) {
      return;
    }

    // Extract padding values, defaulting to 0 if not present
    const paddingLeft = "paddingLeft" in node ? node.paddingLeft : 0;
    const paddingRight = "paddingRight" in node ? node.paddingRight : 0;
    const paddingTop = "paddingTop" in node ? node.paddingTop : 0;
    const paddingBottom = "paddingBottom" in node ? node.paddingBottom : 0;

    // Check if all paddings are the same
    const isLinked = paddingLeft === paddingRight &&
      paddingRight === paddingTop &&
      paddingTop === paddingBottom;

    const padding = {
      unit: "px",
      top: paddingTop?.toString(),
      right: paddingRight?.toString(),
      bottom: paddingBottom?.toString(),
      left: paddingLeft?.toString(),
      isLinked: isLinked
    };

    // Different widgets use different property names for padding
    const widgetSettingsMap: Record<string, Record<string, string>> = {
      "eael-dual-color-header": {
        "eael-dch-container": "eael_dch_container_padding",
      },
      "eael-cta-box": {
        "eael-cta-box-container": "eael_cta_container_padding",
      },
      "eael-info-box": {
        "eael-info-box-container": "eael_section_infobox_container_padding",
        "eael-info-box-button": "eael_creative_button_padding" // bug in the ea. need to rename this control
      },
      "eael-feature-list": {
        "eael-feature-list-icon": "eael_feature_list_icon_padding",
      },
      "eael-counter": {
        "eael-counter-icon": "counter_icon_padding",
        // "eael-counter-container": "counter_container_padding", // not released may be
      },
      "eael-woo-product-list": {
        "eael-woo-product-list-container": "eael_product_list_container_padding",
      },
      "eael-testimonial": {
        "eael-testimonial-container": "eael_testimonial_padding",
      },
    };

    // Handle different widget types
    if (data.widgetType && settingKey && widgetSettingsMap[data.widgetType]?.[settingKey]) {
      data.settings[widgetSettingsMap[data.widgetType][settingKey]] = padding;
    } else {
      // Default padding property for elements without specific widget mapping
      data.settings.padding = padding;
    }
  }

  /**
   * Determines the relative position of two nodes (above/below)
   * @param firstNode The first node to compare
   * @param secondNode The second node to compare
   * @returns 'top' if firstNode is above secondNode, 'default' otherwise
   */
  public static determineRelativePosition(firstNode: any, secondNode: any): 'top' | 'default' {
    // Check if nodes have absoluteBoundingBox
    if ('absoluteBoundingBox' in firstNode && 'absoluteBoundingBox' in secondNode) {
      const firstBoundingBox = firstNode.absoluteBoundingBox as { y: number };
      const secondBoundingBox = secondNode.absoluteBoundingBox as { y: number };

      // If first node's Y position is less than second node's Y position, it's at the top
      if (firstBoundingBox.y < secondBoundingBox.y) {
        return 'top';
      }
    }
    // Alternative check if absoluteBoundingBox is not available
    else if ('y' in firstNode && 'y' in secondNode) {
      const firstY = (firstNode as unknown as { y: number }).y;
      const secondY = (secondNode as unknown as { y: number }).y;

      if (firstY < secondY) {
        return 'top';
      }
    }

    // Default fallback
    return 'default';
  }

  public static processMargin(node: any, data: any, widgetNodeType?: string) {
    // Check if node has itemSpacing property (auto layout property)
    if (!(node.type == "TEXT" || node.name.startsWith('image')) && !("itemSpacing" in node)) return;

    // Skip if settings is an array
    if (Array.isArray(data.settings)) return;

    let itemSpacing = node.itemSpacing || 0;

    // Create margin object based on layout direction
    // In Figma, item spacing applies between items based on the layout direction
    let isHorizontal = "layoutMode" in node && node.layoutMode === "HORIZONTAL";

    let getParent = node.type === "TEXT" || node.name.startsWith('image') ? 1 : 0; // InfoBox: if image (first) and main-content, then need to fetch parent to get image bottom margin

    if (getParent) {
      const parent = node.parent;
      if (parent || ("itemSpacing" in parent)) {
        itemSpacing = parent.itemSpacing || 0;
        isHorizontal = "layoutMode" in parent && parent.layoutMode === "HORIZONTAL";
      }
    }

    // if horizontal and not last child, then add itemSpacing to right margin
    const isLastChild = node.parent?.children.indexOf(node) === node.parent?.children.length - 1;

    // if horizontal and not last child, then add itemSpacing to right margin
    let margin = {
      unit: "px",
      top: isHorizontal ? "0" : "0",
      right: isHorizontal && !isLastChild ? itemSpacing.toString() : "0",
      bottom: isHorizontal ? "0" : itemSpacing.toString(),
      left: isHorizontal ? "0" : "0",
      isLinked: false
    };

    // Map widget types to their margin property names
    const widgetSettingsMap: Record<string, Record<string, string>> = {
      "eael-dual-color-header": {
        "eael-dch-container": "eael_dch_container_margin",
      },
      "eael-cta-box": {
        "eael-cta-box-container": "eael_cta_container_margin",
        "eael-cta-box-title": "eael_cta_title_margin",
      },
      "eael-info-box": {
        "eael-info-box-title": "eael_infobox_title_margin",
        "eael-info-box-subtitle": "eael_infobox_subtitle_margin",
        "eael-info-box-content": "eael_infobox_content_margin",
        "eael-info-box-image": "eael_infobox_img_margin",
        "eael-info-box-main-content": "parent-wrap" // parent-wrap is placeholder value. just to make it not null.
      },
      "eael-testimonial": {
        "eael-testimonial-image": "eael_testimonial_image_margin",
        "eael-testimonial-content": "eael_testimonial_description_margin",
        "eael-testimonial-review": "eael_testimonial_rating_margin",
        "eael-testimonial-star": "eael_testimonial_rating_item_distance",
      },
      "eael-feature-list": {
        "eael-feature-list-icon": "eael_feature_list_icon_space",
        "eael-feature-list-list": "eael_feature_list_space_between",
      },
      "eael-counter": {
        "eael-counter-icon": "counter_icon_margin",
      },
      "eael-woo-product-list": {
        "eael-woo-product-list-container": "counter_icon_margin",
      },
    };

    // Set the margin based on widget type and node type
    if (data.widgetType && widgetNodeType && widgetSettingsMap[data.widgetType]?.[widgetNodeType]) {
      if ('eael-info-box-main-content' == widgetNodeType) {
        // margin will be assigned to direct childrens.
        node.children.forEach((child: any) => {
          if (!Array.isArray(data.settings)) {
            if (child.name === "title") {
              data.settings.eael_infobox_title_margin = margin;
            } else if (child.name === "subtitle") {
              data.settings.eael_infobox_subtitle_margin = margin;
            } else if (child.name === "content") {
              data.settings.eael_infobox_content_margin = margin;
            } else if (child.name === "button") {
              data.settings.eael_infobox_button_margin = margin;
            }
          }
        });
      }
      else if ('eael-dch-container' == widgetNodeType || 'eael-cta-box-container' == widgetNodeType) {
        margin.right = '0';
        margin.bottom = '0';
        data.settings[widgetSettingsMap[data.widgetType][widgetNodeType]] = margin;
      }
      else if ('eael-feature-list-icon' == widgetNodeType) {
        data.settings[widgetSettingsMap[data.widgetType][widgetNodeType]] = {
          unit: 'px',
          size: isHorizontal ? margin.right : margin.bottom,
          sizes: []
        };
      }
      else if ('eael-feature-list-list' == widgetNodeType) {
        data.settings[widgetSettingsMap[data.widgetType][widgetNodeType]] = {
          unit: 'px',
          size: isHorizontal ? margin.right : margin.bottom,
          sizes: []
        };

        data.settings.eael_feature_list_title_bottom_space = {
          unit: 'px',
          size: isHorizontal ? margin.right : margin.bottom,
          sizes: []
        };
      }
      else {
        data.settings[widgetSettingsMap[data.widgetType][widgetNodeType]] = margin;
      }
    } else {
      data.settings.margin = margin;
    }
  }

  public static calculateSpacingBetweenNodes(node1: any, node2: any): number | null {
    if (!node1 || !node2) return null;

    // Get the absolute positions of both nodes
    const pos1 = {
      x: node1.absoluteBoundingBox?.x || node1.x,
      y: node1.absoluteBoundingBox?.y || node1.y,
      width: node1.absoluteBoundingBox?.width || node1.width,
      height: node1.absoluteBoundingBox?.height || node1.height
    };

    const pos2 = {
      x: node2.absoluteBoundingBox?.x || node2.x,
      y: node2.absoluteBoundingBox?.y || node2.y,
      width: node2.absoluteBoundingBox?.width || node2.width,
      height: node2.absoluteBoundingBox?.height || node2.height
    };

    // Calculate vertical spacing
    const verticalSpacing = Math.abs(pos2.y - (pos1.y + pos1.height));

    // Calculate horizontal spacing
    const horizontalSpacing = Math.abs(pos2.x - (pos1.x + pos1.width));

    // Return the smaller spacing value
    return Math.min(verticalSpacing, horizontalSpacing);
  }
}