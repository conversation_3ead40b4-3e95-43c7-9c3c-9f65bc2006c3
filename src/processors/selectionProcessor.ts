import { ElementTypeResolver } from '../utils/elementTypeResolver';
import * as WidgetProcessors from '../widgets';
import { JsonResult, SceneNode, FrameNode, ElementData } from '../types/figma';
import { WidgetUtils } from '../utils/widgetUtils';
import { PLUGIN_TITLE, PLUGIN_VERSION } from '../code';

export class SelectionProcessor {

  public static nodeIds: string[] = []; // Store all matching node IDs

  /**
 * Process the selection and return a JSON result
 */
  public static async processSelection(selection: readonly SceneNode[]): Promise<JsonResult> {
    // Reset nodeIds array before collecting new ones
    this.nodeIds = [];

    // Collect image node IDs from the selection
    this.collectNodeIds(selection as any);

    // Process the selection to generate content
    const content = selection.map((node) => this.processNode(node))
      .filter(elementData => elementData !== null);

    return {
      content,
      page_settings: [],
      version: PLUGIN_VERSION,
      title: PLUGIN_TITLE,
      type: "section"
    };
  }

  private static processNode(node: SceneNode): ElementData {
    if (!node) {
      return {
        // id: '',
        id: this.generate8CharHash(),
        settings: [],
        elements: [],
        isInner: false,
        elType: 'container'
      };
    }

    const elType = ElementTypeResolver.getElementType(node);
    const isInner = node.name?.includes("inner") || false;
    const baseData: ElementData = {
      // id: node.id || '',
      id: this.generate8CharHash(),
      settings: {},
      elements: [],
      isInner,
      elType
    };

    // Process container settings if it's a container
    if (elType === 'container') {
      // Process common container settings
      WidgetUtils.processBackground(node, baseData); // debug here

      WidgetUtils.processBorderRadius(node, baseData);
      WidgetUtils.processBorderWidth(node, baseData, 'container');
      WidgetUtils.processPadding(node, baseData);
      WidgetUtils.processMargin(node, baseData);

      // Process widget-specific settings
      WidgetUtils.processWidgetSettings(node, baseData);
    }

    // Process as widget if it's a text node or has widget- prefix
    if (node.type === "TEXT" || node.name?.startsWith('widget-')) {
      const widgetType = node.name?.startsWith('widget-')
        ? node.name.substring(7)
        : "heading";
      baseData.widgetType = widgetType;

      // Process widget-specific settings
      this.processWidgetByType(node, baseData);
    }

    // Process children for non-widget elements
    if ('children' in node && Array.isArray(node.children) &&
      !baseData.widgetType?.startsWith('eael') && baseData.widgetType !== "button") {
      baseData.elements = node.children.map(child => this.processNode(child));
    }

    // Clean up empty settings
    if (Object.keys(baseData.settings).length === 0) {
      baseData.settings = [];
    }

    return baseData;
  }

  private static processWidgetByType(node: SceneNode, baseData: ElementData) {
    const widgetProcessors = {
      'eael-dual-color-header': WidgetProcessors.DualColorHeadingWidgetProcessor,
      'eael-creative-button': WidgetProcessors.CreativeButtonWidgetProcessor,
      'eael-cta-box': WidgetProcessors.CtaBoxWidgetProcessor,
      'eael-counter': WidgetProcessors.CounterWidgetProcessor,
      'heading': WidgetProcessors.HeadingWidgetProcessor,
      'text-editor': WidgetProcessors.TextEditorWidgetProcessor,
      'eael-feature-list': WidgetProcessors.FeatureListWidgetProcessor,
      'eael-info-box': WidgetProcessors.InfoBoxWidgetProcessor,
      'eael-testimonial': WidgetProcessors.TestimonialWidgetProcessor,
      'eael-advanced-menu': WidgetProcessors.AdvancedMenuWidgetProcessor,
      'image': WidgetProcessors.ImageWidgetProcessor
    };

    if (baseData.widgetType in widgetProcessors) {
      widgetProcessors[baseData.widgetType].process(node as FrameNode, baseData);
    } else if (baseData.widgetType?.startsWith('eael-advanced-menu')) {
      WidgetProcessors.AdvancedMenuWidgetProcessor.process(node as FrameNode, baseData);
    } else {
      WidgetUtils.processWidgetSettings(node, baseData);
    }
  }

  private static generate8CharHash(): string {
    return Math.random().toString(36).substring(2, 10); // 8 chars
  }

  // This section was intentionally removed as it's no longer needed.
  // Image processing is now handled in the PluginUI class.

  // Helper function to collect valid node IDs
  public static collectNodeIds(nodes: readonly any[]) {
    for (const node of nodes) {
      // Check for image nodes by name or type
      if ((node.name.toLowerCase().startsWith("image") || node.type === "RECTANGLE") && "fills" in node) {
        // Check if the node has an image fill
        const hasFills = node.fills && Array.isArray(node.fills) && node.fills.length > 0;
        const hasImageFill = hasFills && node.fills.some((fill: any) => fill.type === "IMAGE");

        if (hasImageFill) {
          // console.log(`Found image node: ${node.id}, ${node.name}, ${node.type}`);
          this.nodeIds.push(node.id);
        }
      }

      // Process children recursively
      if ("children" in node && Array.isArray(node.children)) {
        this.collectNodeIds(node.children);
      }
    }
  }
}