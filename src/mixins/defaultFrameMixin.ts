export interface DefaultFrameMixin {
  id: string;
  name: string;
  type: string;
  children?: DefaultFrameMixin[];
  layoutMode?: "NONE" | "HORIZONTAL" | "VERTICAL";
  primaryAxisAlignItems?: "MIN" | "CENTER" | "MAX" | "SPACE_BETWEEN";
  counterAxisAlignItems?: "MIN" | "CENTER" | "MAX" | "SPACE_BETWEEN";
  itemSpacing?: number;
  paddingTop?: number;
  paddingRight?: number;
  paddingBottom?: number;
  paddingLeft?: number;
  constraints?: {
    horizontal: "MIN" | "CENTER" | "MAX" | "STRETCH" | "SCALE";
    vertical: "MIN" | "CENTER" | "MAX" | "STRETCH" | "SCALE";
  };
  fills?: Array<{
    type: string;
    color?: { r: number; g: number; b: number; a: number };
    gradientStops?: Array<{ color: { r: number; g: number; b: number; a: number } }>;
    imageHash?: string;
    visible?: boolean;
    opacity?: number;
    blendMode?: string;
    scaleMode?: string;
    imageTransform?: number[][];
    scalingFactor?: number;
    rotation?: number;
  }>;
  strokes?: Array<{
    type: string;
    color: { r: number; g: number; b: number; a: number };
    visible?: boolean;
    opacity?: number;
    blendMode?: string;
  }>;
  strokeWeight?: number;
  cornerRadius?: number;
  width?: number;
  height?: number;
  characters?: string;
  fontName?: {
    family: string;
    style: string;
  };
  fontSize?: number;
  lineHeight?: {
    unit: string;
    value: number;
  };
  letterSpacing?: {
    unit: string;
    value: number;
  };
  textCase?: string;
  textAlignHorizontal?: string;
  textAlignVertical?: string;
  visible?: boolean;
  opacity?: number;
  blendMode?: string;
  effects?: Array<{
    type: string;
    visible: boolean;
    color?: { r: number; g: number; b: number; a: number };
    offset?: { x: number; y: number };
    radius?: number;
    spread?: number;
  }>;
  effectStyleId?: string;
  strokeStyleId?: string;
  fillStyleId?: string;
  backgroundStyleId?: string;
  textStyleId?: string;
  gridStyleId?: string;
  clipsContent?: boolean;
  guides?: Array<{
    axis: "X" | "Y";
    offset: number;
  }>;
  layoutGrids?: Array<{
    pattern: "GRID" | "COLUMNS" | "ROWS";
    sectionSize?: number;
    visible?: boolean;
    color?: { r: number; g: number; b: number; a: number };
  }>;
  exportSettings?: Array<{
    suffix: string;
    format: "JPG" | "PNG" | "SVG" | "PDF";
    constraint?: {
      type: "SCALE" | "WIDTH" | "HEIGHT";
      value: number;
    };
  }>;
} 