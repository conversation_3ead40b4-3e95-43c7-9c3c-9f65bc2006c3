declare global {
  interface SceneNode {
    id: string;
    name: string;
    type: string;
    visible: boolean;
    locked: boolean;
    parent: BaseNode | null;
    children?: readonly SceneNode[];
    characters?: string;
    fontSize?: number;
    fontName?: FontName;
    lineHeight?: LineHeight;
    letterSpacing?: LetterSpacing;
    fills?: readonly <PERSON>[];
    strokes?: readonly <PERSON>[];
    strokeWeight?: number;
    paddingTop?: number;
    paddingRight?: number;
    paddingBottom?: number;
    paddingLeft?: number;
    marginTop?: number;
    marginRight?: number;
    marginBottom?: number;
    marginLeft?: number;
    width?: number;
    height?: number;
    absoluteTransform?: Transform;
    relativeTransform?: Transform;
    x?: number;
    y?: number;
    rotation?: number;
    layoutMode?: 'NONE' | 'HORIZONTAL' | 'VERTICAL';
    primaryAxisSizingMode?: 'FIXED' | 'AUTO';
    counterAxisSizingMode?: 'FIXED' | 'AUTO';
    primaryAxisAlignItems?: 'MIN' | 'CENTER' | 'MAX' | 'SPACE_BETWEEN';
    counterAxisAlignItems?: 'MIN' | 'CENTER' | 'MAX' | 'SPACE_BETWEEN';
    itemSpacing?: number;
    layoutWrap?: 'NO_WRAP' | 'WRAP';
    layoutAlign?: 'STRETCH' | 'INHERIT';
    layoutGrow?: number;
    layoutPositioning?: 'AUTO' | 'ABSOLUTE';
    opacity?: number;
    blendMode?: BlendMode;
    isMask?: boolean;
    effects?: readonly Effect[];
    effectStyleId?: string;
    backgroundStyleId?: string;
    fillStyleId?: string;
    strokeStyleId?: string;
    gridStyleId?: string;
    exportSettings?: readonly ExportSetting[];
    componentPropertyReferences?: ComponentPropertyReferences;
    componentProperties?: ComponentProperties;
    componentPropertyDefinitions?: ComponentPropertyDefinitions;
  }

  interface BaseNode {
    id: string;
    name: string;
    type: string;
    visible: boolean;
    locked: boolean;
    parent: BaseNode | null;
  }

  interface FontName {
    family: string;
    style: string;
  }

  interface LineHeight {
    value: number;
    unit: 'PIXELS' | 'PERCENT';
  }

  interface LetterSpacing {
    value: number;
    unit: 'PIXELS' | 'PERCENT';
  }

  interface Paint {
    type: PaintType;
    visible?: boolean;
    opacity?: number;
    color?: RGB;
    blendMode?: BlendMode;
    gradientStops?: readonly ColorStop[];
    gradientTransform?: Transform;
  }

  interface RGB {
    r: number;
    g: number;
    b: number;
  }

  interface ColorStop {
    position: number;
    color: RGB;
  }

  interface Transform {
    a: number;
    b: number;
    c: number;
    d: number;
    tx: number;
    ty: number;
  }

  type PaintType = 'SOLID' | 'GRADIENT_LINEAR' | 'GRADIENT_RADIAL' | 'GRADIENT_ANGULAR' | 'GRADIENT_DIAMOND' | 'IMAGE' | 'EMOJI';
  type BlendMode = 'PASS_THROUGH' | 'NORMAL' | 'DARKEN' | 'MULTIPLY' | 'LINEAR_BURN' | 'COLOR_BURN' | 'LIGHTEN' | 'SCREEN' | 'LINEAR_DODGE' | 'COLOR_DODGE' | 'OVERLAY' | 'SOFT_LIGHT' | 'HARD_LIGHT' | 'DIFFERENCE' | 'EXCLUSION' | 'HUE' | 'SATURATION' | 'COLOR' | 'LUMINOSITY';

  interface Effect {
    type: EffectType;
    visible?: boolean;
    radius?: number;
    color?: RGB;
    offset?: Vector;
    spread?: number;
    blendMode?: BlendMode;
  }

  type EffectType = 'INNER_SHADOW' | 'DROP_SHADOW' | 'LAYER_BLUR' | 'BACKGROUND_BLUR';

  interface Vector {
    x: number;
    y: number;
  }

  interface ExportSetting {
    suffix: string;
    format: ExportFormat;
    constraint: Constraint;
  }

  type ExportFormat = 'JPG' | 'PNG' | 'SVG' | 'PDF';

  interface Constraint {
    type: 'SCALE' | 'WIDTH' | 'HEIGHT';
    value: number;
  }

  interface ComponentPropertyReferences {
    [key: string]: string;
  }

  interface ComponentProperties {
    [key: string]: ComponentProperty;
  }

  interface ComponentPropertyDefinitions {
    [key: string]: ComponentPropertyDefinition;
  }

  interface ComponentProperty {
    type: ComponentPropertyType;
    value: boolean | string | number;
  }

  interface ComponentPropertyDefinition {
    type: ComponentPropertyType;
    defaultValue: boolean | string | number;
  }

  type ComponentPropertyType = 'BOOLEAN' | 'TEXT' | 'INSTANCE_SWAP' | 'VARIANT';
} 