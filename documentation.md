Figma: https://www.figma.com/design/A3bQqOmIKvnk8oIDVW1XKr/Figma-to-Elementor?node-id=44-318&t=ENsadqyV3mnR7EvL-1

Design Practices
- Use auto layout; Hug Contents (most cases); 
- Remove group if possible
- (may be) add container for each columns (if design gets broken)
- (may be) use hug contents as much as possible
- Name frame as section or column
- All section must have column
- Inner section must have column (Section => Column => Inner Section => Column)
- Add inner-column under inner-section (inner helps to add isInner true; required for section. but inner-column could be optional. for now could'nt find a way to skip inner for column)
- If Parent height is same as child height, we will skip child height (designers doesnt add height for child if parent has same height)
- Content within inner-section => isInner is true for both section and column elType (if current section name is inner-section or parent has isInner true, then item has isInner true)
- (optional for now) Now, treating heading node as widget. we can treat text node as widget or from name 'widget-widget-name'; e.x. widget-e-heading says to use elementor heading widget
- figma has no heading tag; so using static tag h2; will use correct font size.
- text node name starts with `widget-` prefix. Ex. widget-{widget-name}, widget-heading, widget-text-editor, widget-eael-filterable-gallery;
- inner-section cant be 100% width; available 3 options: 50/50, 33/66, 66/33
- Child of inner section should always be a widget. Structure: Section -> Column -> Inner Section -> Inner Column -> Widget1 , Widget 2
- No svg support. Added fa icon support for now.
- Icon widget naming: icon- prefix. Then font awesome solid (fas) icon name. 
E.x. `icon-fa-long-arrow-alt-right`. (will be converted to `fas fa-long-arrow-alt-right`)
- Icon position (left / right): if button frame has 2 childs and first child is text and 2nd child is icon, then icon position on the right.
- Inner column naming: inner-column (width 100%), inner-column-6 (width 50%), inner-column-4 (width 33.3%), inner-column-3 (width 25%), following bootstrap format of 12 columns
- Auto Layout and Hug Content is needed to avoid design break (may be)
- Alignment based on parent alignment in most cases.

1. Creative Button
    - container
    - widget-eael-creative-button
    - icon-fa-long-arrow-alt-right-rotation-240 (rotation 240 deg)

2. Dual Color Heading
    - container
    - widget-eael-dual-color-header (text node possibly)

3. Advanced Menu: 
    - container
    - widget-eael-advanced-menu (add bg here. fill in figma; add menu id in the end if possible; 
            e.x. widget-eael-advanced-menu-298)
    - menu-item (container of text node; all menu item should have a container;)
    - active-menu-item (container of active text node)

4. Call to Action 
    - container
    - widget-eael-cta-box
    - title
    - content
    - button (button-primary, button-secondary)

5. Info Box
    - container
    - widget-eael-info-box (add widget container background and style here.)
    - main-content (sibling of image and immediate child of widget-*)
    - image (sibling of main content and immediate child of widget-*)
    - title or starts with 'title -'  
    - subtitle or starts with 'subtitle -'  
    - content or starts with 'content -'
    - button (to avoid default bg color, set bg color on figma and border too; similar to the bg;)
        icon rotation: last word as rotationXXX (positive or negative deg)
            Ex. icon-fa-long-arrow-alt-right-rotation320 means 320 deg
                icon-fa-long-arrow-alt-right-rotation-320 means -320 deg
    - image or icon (if child has image frame then collect. 
            if no child image found, check if frame has bg image)

6. Post Carousel
    - container
    - widget-eael-post-carousel
    - post (as sibling to determine count) - needed for post border radius
    - image
    - main-content 
        - title (text node)
        - content (paragraph; text node)
    - controls (will take padding bottom from parent's gap)

7. Testimonial
    - container
    - widget-eael-testimonial
    - review
        - star
    - content
    - image
    - name
    - company

8. Feature List
    - container
    - widget-eael-feature-list
    - list
    - title
    - content
    - icon

9. Counter
    - container
    - widget-eael-counter
    - number
    - title

10. Logo Carousel (Partial)
    - container
    - image

11. Woo Product List (Partial)
    - container
    - widget-eael-woo-product-list
    - image
    - review
        - star, half-star, count
    - category-name
    - category-icon
    - title
    - content
    - regular-price
    - sale-price
    - total-sold (child: text node, progress bar etc)
    - button-add-to-cart (icon and text node)
    - button-view-product 

12. TBA
    - container
    - 


Elementor Widgets
1. Button widget
    - container
    - widget-button : on text node container

2. Heading
    - container
    - widget-heading : on text node

3. Text Editor widget
    - container
    - widget-text-editor : on text node

Mapping:
- Frame => Section
- border => stroke; border width => strokeWeight
- background => fills
- text color => fills (if node type = text)
- CTA BOX WIDGET
    - main widget container name should be : widget-eael-cta-box 
    - name title container as title
    - name content container as content
    - name button container as button or, (primary-button or secondary-button) -> update maybe: should starts with button; for example, button-primary or button-secondary

Elementor:
- Everything added under section (or container - later)
- column can be added under section
- All widgets are added under Section -> Column
- Structure: Section (*) => Column (*) => Widget
- SVG icon in template library json doesn't work after importing.
- (PNG) image import not working via json
- Container doesnt require inner column (section needs inner column though).
